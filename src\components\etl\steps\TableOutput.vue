<template>
  <div class="table-output-config">
    <el-form label-position="top" size="small">
      <el-form-item label="数据库连接">
        <el-select v-model="config.connection" placeholder="请选择数据库连接">
          <el-option
            v-for="conn in dbConnections"
            :key="conn.id"
            :label="conn.name"
            :value="conn.id"
          />
        </el-select>
      </el-form-item>

      <el-form-item label="目标表">
        <el-select 
          v-model="config.table"
          placeholder="请选择目标表"
          filterable
          allow-create>
          <el-option
            v-for="table in tables"
            :key="table"
            :label="table"
            :value="table"
          />
        </el-select>
      </el-form-item>

      <el-form-item label="提交记录数">
        <el-input-number 
          v-model="config.commitSize"
          :min="1"
          :max="10000"
        />
      </el-form-item>

      <el-form-item label="字段映射">
        <div class="field-mapping">
          <div class="mapping-header">
            <span>输入字段</span>
            <span>目标字段</span>
          </div>
          <div
            v-for="(mapping, index) in config.fieldMappings"
            :key="index"
            class="mapping-row">
            <el-select v-model="mapping.input" placeholder="选择输入字段">
              <el-option
                v-for="field in inputFields"
                :key="field.name"
                :label="field.name"
                :value="field.name"
              />
            </el-select>
            <el-select v-model="mapping.output" placeholder="选择目标字段">
              <el-option
                v-for="field in tableFields"
                :key="field.name"
                :label="field.name"
                :value="field.name"
              />
            </el-select>
            <el-button
              type="text"
              icon="el-icon-delete"
              @click="removeMapping(index)"
            />
          </div>
          <el-button
            type="text"
            icon="el-icon-plus"
            @click="addMapping">
            添加映射
          </el-button>
        </div>
      </el-form-item>

      <el-form-item>
        <el-checkbox v-model="config.truncateTable">执行前清空表</el-checkbox>
      </el-form-item>
      
      <el-form-item>
        <el-checkbox v-model="config.specifyFields">指定要更新的字段</el-checkbox>
      </el-form-item>

      <el-form-item>
        <el-checkbox v-model="config.ignoreErrors">忽略插入错误</el-checkbox>
      </el-form-item>

      <el-form-item>
        <el-checkbox v-model="config.useBatchUpdate">使用批量更新</el-checkbox>
      </el-form-item>

      <template v-if="config.specifyFields">
        <el-divider content-position="left">更新字段</el-divider>
        <div class="update-fields">
          <el-checkbox-group v-model="config.updateFields">
            <el-checkbox
              v-for="field in tableFields"
              :key="field.name"
              :label="field.name">
              {{ field.name }}
            </el-checkbox>
          </el-checkbox-group>
        </div>
      </template>
    </el-form>
  </div>
</template>

<script>
export default {
  name: 'TableOutput',
  props: {
    value: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      config: {
        connection: '',
        table: '',
        commitSize: 1000,
        truncateTable: false,
        specifyFields: false,
        ignoreErrors: false,
        useBatchUpdate: true,
        fieldMappings: [],
        updateFields: []
      },
      dbConnections: [],
      tables: [],
      inputFields: [],
      tableFields: []
    }
  },
  watch: {
    value: {
      handler(val) {
        this.config = { ...this.config, ...val }
      },
      immediate: true,
      deep: true
    },
    config: {
      handler(val) {
        this.$emit('input', val)
      },
      deep: true
    },
    'config.connection': {
      async handler(val) {
        if (val) {
          await this.loadTables()
        }
      }
    },
    'config.table': {
      async handler(val) {
        if (val) {
          await this.loadTableFields()
        }
      }
    }
  },
  methods: {
    addMapping() {
      this.config.fieldMappings.push({
        input: '',
        output: ''
      })
    },
    removeMapping(index) {
      this.config.fieldMappings.splice(index, 1)
    },
    async loadTables() {
      try {
        // TODO: 调用获取表列表接口
      } catch (error) {
        this.$message.error('获取表列表失败：' + error.message)
      }
    },
    async loadTableFields() {
      try {
        // TODO: 调用获取表字段接口
      } catch (error) {
        this.$message.error('获取表字段失败：' + error.message)
      }
    }
  },
  async created() {
    try {
      // TODO: 获取数据库连接列表
    } catch (error) {
      console.error('获取数据库连接失败：', error)
    }
  }
}
</script>

<style lang="scss" scoped>
.table-output-config {
  padding: 16px;

  .field-mapping {
    border: 1px solid #dcdfe6;
    border-radius: 4px;
    padding: 16px;

    .mapping-header {
      display: flex;
      justify-content: space-around;
      margin-bottom: 8px;
      font-weight: bold;
    }

    .mapping-row {
      display: flex;
      align-items: center;
      margin-bottom: 8px;
      gap: 8px;

      .el-select {
        flex: 1;
      }
    }
  }

  .update-fields {
    margin-top: 8px;
    
    .el-checkbox-group {
      display: grid;
      grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
      gap: 8px;
    }
  }
}
</style>
