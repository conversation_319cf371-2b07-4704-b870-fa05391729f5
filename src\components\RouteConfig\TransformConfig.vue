<template>
  <div class="transform-config">
    <el-form label-width="100px">
      <el-form-item label="转换类型">
        <el-select v-model="config.transformType">
          <el-option label="字段映射" value="field" />
          <el-option label="表达式" value="expression" />
          <el-option label="模板" value="template" />
        </el-select>
      </el-form-item>

      <!-- 字段映射配置 -->
      <template v-if="config.transformType === 'field'">
        <div v-for="(mapping, index) in config.fieldMappings" :key="index" class="mapping-item">
          <el-input v-model="mapping.from" placeholder="源字段" style="width: 200px;" />
          <span class="mapping-arrow">→</span>
          <el-input v-model="mapping.to" placeholder="目标字段" style="width: 200px;" />
          <el-select v-model="mapping.type" placeholder="类型" style="width: 120px; margin: 0 10px;">
            <el-option label="字符串" value="string" />
            <el-option label="数字" value="number" />
            <el-option label="日期" value="date" />
            <el-option label="布尔" value="boolean" />
          </el-select>
          <el-button type="text" @click="removeMapping(index)">删除</el-button>
        </div>
        <el-button type="text" @click="addMapping">添加映射</el-button>
      </template>

      <!-- 表达式配置 -->
      <template v-if="config.transformType === 'expression'">
        <el-form-item label="表达式">
          <el-input
            type="textarea"
            v-model="config.expression"
            :rows="4"
            placeholder="请输入表达式，例如：$.name.toUpperCase()"
          />
        </el-form-item>
      </template>

      <!-- 模板配置 -->
      <template v-if="config.transformType === 'template'">
        <el-form-item label="模板">
          <el-input
            type="textarea"
            v-model="config.template"
            :rows="6"
            placeholder="请输入模板，支持${variable}形式的变量"
          />
        </el-form-item>
      </template>
    </el-form>
  </div>
</template>

<script>
export default {
  name: 'TransformConfig',
  props: {
    value: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      config: {
        transformType: 'field',
        fieldMappings: [],
        expression: '',
        template: ''
      }
    }
  },
  watch: {
    value: {
      handler(val) {
        this.config = { ...this.config, ...val }
      },
      immediate: true
    },
    config: {
      handler(val) {
        this.$emit('input', val)
      },
      deep: true
    }
  },
  methods: {
    addMapping() {
      this.config.fieldMappings.push({
        from: '',
        to: '',
        type: 'string'
      })
    },
    removeMapping(index) {
      this.config.fieldMappings.splice(index, 1)
    }
  }
}
</script>

<style scoped>
.mapping-item {
  display: flex;
  align-items: center;
  margin-bottom: 10px;
}
.mapping-arrow {
  margin: 0 10px;
  color: #909399;
}
</style> 