import request from '@/utils/request'

// ESB接口配置基础URL
const baseUrl = '/esb/config'

// 创建接口配置
export function createConfig(data) {
  return request({
    url: baseUrl + '/create',
    method: 'post',
    data
  })
}

// 更新接口配置
export function updateConfig(data) {
  return request({
    url: baseUrl + '/update',
    method: 'put',
    data
  })
}

// 删除接口配置
export function deleteConfig(id) {
  return request({
    url: baseUrl + '/delete?id=' + id,
    method: 'delete'
  })
}

// 获取接口配置详情
export function getConfig(id) {
  return request({
    url: baseUrl + '/get?id=' + id,
    method: 'get'
  })
}

// 获取接口配置分页
export function getConfigPage(query) {
  return request({
    url: baseUrl + '/page',
    method: 'get',
    params: query
  })
}

// 获取指定API的配置列表
export function getConfigListByApiId(apiId) {
  return request({
    url: baseUrl + '/list-by-api?apiId=' + apiId,
    method: 'get'
  })
} 