import request from '@/utils/request'

// 创建ETL作业监控
export function createJobMonitor(data) {
  return request({
    url: '/etl/job-monitor/create',
    method: 'post',
    data: data
  })
}

// 更新ETL作业监控
export function updateJobMonitor(data) {
  return request({
    url: '/etl/job-monitor/update',
    method: 'put',
    data: data
  })
}

// 删除ETL作业监控
export function deleteJobMonitor(id) {
  return request({
    url: '/etl/job-monitor/delete',
    method: 'delete',
    params: { id }
  })
}

// 获取ETL作业监控详情
export function getJobMonitor(id) {
  return request({
    url: '/etl/job-monitor/get',
    method: 'get',
    params: { id }
  })
}

// 获取所有启用的ETL作业监控
export function getEnabledJobMonitorList() {
  return request({
    url: '/etl/job-monitor/list-all-enabled',
    method: 'get'
  })
}

// 根据作业编号获取ETL作业监控
export function getJobMonitorByJobId(jobId) {
  return request({
    url: '/etl/job-monitor/get-by-job-id',
    method: 'get',
    params: { jobId }
  })
}

// 获取ETL作业监控分页
export function getJobMonitorPage(query) {
  return request({
    url: '/etl/job-monitor/page',
    method: 'get',
    params: query
  })
} 