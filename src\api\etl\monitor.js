import request from '@/utils/request';

// 查询作业监控列表
export function listMonitor(query) {
  return request({
    url: '/etl/job-monitor/list',
    method: 'get',
    params: query
  });
}

// 查询作业监控详情
export function getMonitor(id) {
  return request({
    url: '/etl/job-monitor/' + id,
    method: 'get'
  });
}

// 停止作业
export function stopJob(id) {
  return request({
    url: '/etl/job-monitor/stop/' + id,
    method: 'put'
  });
}

// 获取作业日志
export function getJobLog(id) {
  return request({
    url: '/etl/job-monitor/log/' + id,
    method: 'get'
  });
}

// 导出作业监控列表
export function exportMonitor(query) {
  return request({
    url: '/etl/job-monitor/export',
    method: 'get',
    params: query
  });
}
