import request from '@/utils/request'

// 获取报表列表
export function listReport(query) {
  return request({
    url: '/report/list',
    method: 'get',
    params: query
  })
}

// 获取报表详情
export function getReport(id) {
  return request({
    url: `/report/${id}`,
    method: 'get'
  })
}

// 新增报表
export function addReport(data) {
  return request({
    url: '/report',
    method: 'post',
    data
  })
}

// 修改报表
export function updateReport(data) {
  return request({
    url: '/report',
    method: 'put',
    data
  })
}

// 删除报表
export function deleteReport(id) {
  return request({
    url: `/report/${id}`,
    method: 'delete'
  })
}

// 预览报表
export function previewReport(data) {
  return request({
    url: '/report/preview',
    method: 'post',
    data
  })
}

// 导出报表
export function exportReport(data) {
  return request({
    url: '/report/export',
    method: 'post',
    responseType: 'blob',
    data
  })
}

// 复制报表
export function copyReport(id) {
  return request({
    url: `/report/${id}/copy`,
    method: 'post'
  })
}

// 移动报表
export function moveReport(data) {
  return request({
    url: '/report/move',
    method: 'post',
    data
  })
}

// 获取报表分类
export function listCategory() {
  return request({
    url: '/report/category',
    method: 'get'
  })
}

// 新增报表分类
export function addCategory(data) {
  return request({
    url: '/report/category',
    method: 'post',
    data
  })
}

// 修改报表分类
export function updateCategory(data) {
  return request({
    url: '/report/category',
    method: 'put',
    data
  })
}

// 删除报表分类
export function deleteCategory(id) {
  return request({
    url: `/report/category/${id}`,
    method: 'delete'
  })
}

// 获取报表模板
export function listTemplate(query) {
  return request({
    url: '/report/template/list',
    method: 'get',
    params: query
  })
}

// 获取报表模板详情
export function getTemplate(id) {
  return request({
    url: `/report/template/${id}`,
    method: 'get'
  })
}

// 新增报表模板
export function addTemplate(data) {
  return request({
    url: '/report/template',
    method: 'post',
    data
  })
}

// 修改报表模板
export function updateTemplate(data) {
  return request({
    url: '/report/template',
    method: 'put',
    data
  })
}

// 删除报表模板
export function deleteTemplate(id) {
  return request({
    url: `/report/template/${id}`,
    method: 'delete'
  })
}

// 导入报表
export function importReport(data) {
  return request({
    url: '/report/import',
    method: 'post',
    data
  })
}

// 获取导入记录
export function listImport(query) {
  return request({
    url: '/report/import/list',
    method: 'get',
    params: query
  })
}

// 重试导入
export function retryImport(id) {
  return request({
    url: `/report/import/${id}/retry`,
    method: 'post'
  })
}

// 获取导入记录详情
export function getImport(id) {
  return request({
    url: `/report/import/${id}`,
    method: 'get'
  })
}

// 删除导入记录
export function deleteImport(id) {
  return request({
    url: `/report/import/${id}`,
    method: 'delete'
  })
}

// 清空导入记录
export function clearImport() {
  return request({
    url: '/report/import/clear',
    method: 'delete'
  })
}

// 导出报表模板
export function exportTemplate() {
  return request({
    url: '/report/template/export',
    method: 'get',
    responseType: 'blob'
  })
} 