import request from '@/utils/request'

// ESB接口基础URL
const baseUrl = '/esb/api'

// 创建接口
export function createApi(data) {
  return request({
    url: baseUrl + '/create',
    method: 'post',
    data
  })
}

// 更新接口
export function updateApi(data) {
  return request({
    url: baseUrl + '/update',
    method: 'put',
    data
  })
}

// 删除接口
export function deleteApi(id) {
  return request({
    url: baseUrl + '/delete?id=' + id,
    method: 'delete'
  })
}

// 获取接口详情
export function getApi(id) {
  return request({
    url: baseUrl + '/get?id=' + id,
    method: 'get'
  })
}

// 获取接口分页
export function getApiPage(query) {
  return request({
    url: baseUrl + '/page',
    method: 'get',
    params: query
  })
}

// 导出接口
export function exportApi(query) {
  return request({
    url: baseUrl + '/export-excel',
    method: 'get',
    params: query,
    responseType: 'blob'
  })
}

// 测试接口
export function testApi(data) {
  return request({
    url: '/esb/proxy/request',
    method: 'post',
    data
  })
} 