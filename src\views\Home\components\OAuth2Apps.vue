<template>
  <div class="oauth2-apps">
    <el-card class="box-card">
      <template #header>
        <div class="card-header">
          <span>
            <el-icon><Connection /></el-icon>
            统一门户
          </span>
          <div class="header-actions">
            <el-button
              type="text"
              @click="refreshApps"
              :loading="isLoading"
              size="small"
            >
              刷新
            </el-button>
            <el-button
              type="text"
              @click="showSettings = !showSettings"
              size="small"
            >
              设置
            </el-button>
          </div>
        </div>
      </template>

      <!-- 设置面板 -->
      <div v-if="showSettings" class="settings-panel">
        <div class="setting-item">
          <span>卡片宽度: {{ cardWidth }}px</span>
          <el-slider
            v-model="cardWidth"
            :min="120"
            :max="250"
            :step="10"
            style="width: 200px; margin-left: 20px;"
            @change="saveCardWidth"
          />
        </div>
        <div class="setting-item">
          <span>预计每行: {{ estimatedCardsPerRow }} 个</span>
        </div>
      </div>

      <!-- 加载中状态 -->
      <div v-if="isLoading && appList.length === 0" class="loading-state">
        <el-icon class="is-loading"><Loading /></el-icon>
        <p>正在加载应用...</p>
      </div>

      <!-- 空状态 -->
      <div v-else-if="!isLoading && appList.length === 0" class="empty-state">
        <el-icon><InfoFilled /></el-icon>
        <p>暂无可用的集成应用</p>
        <el-button type="primary" @click="refreshApps" size="small">
          重新加载
        </el-button>
      </div>

      <!-- 应用网格 -->
      <div
        v-else
        v-loading="isLoading"
        class="apps-grid"
        :style="{ '--card-width': cardWidth + 'px' }"
      >
        <div
          v-for="app in appList"
          :key="app.id"
          class="app-item"
          @click="handleAppClick(app)"
          :style="{ width: cardWidth + 'px', height: cardWidth + 'px' }"
        >
          <div class="app-icon">
            <img :src="app.icon || defaultIcon" :alt="app.name" />
          </div>
          <div class="app-info">
            <div class="app-name">{{ app.name }}</div>
            <div v-if="app.description" class="app-description">{{ app.description }}</div>
          </div>
          <div v-if="app.status === 0" class="app-status disabled">已禁用</div>
        </div>
      </div>
    </el-card>
  </div>
</template>

<script lang="ts">
import { defineComponent, ref, onMounted, computed } from 'vue'
import { ElMessage } from 'element-plus'
import { Connection, Loading, InfoFilled } from '@element-plus/icons-vue'
import { getAccessibleOAuth2Apps, authorizeApp, type OAuth2ClientVO } from '@/api/system/oauth2/client'
import defaultIconSvg from '@/assets/svgs/icon.svg'

export default defineComponent({
  name: 'OAuth2Apps',
  components: {
    Connection,
    Loading,
    InfoFilled
  },
  setup() {
    const isLoading = ref(false)
    const accessibleApps = ref<OAuth2AppVO[]>([])
    const showSettings = ref(false)
    const cardWidth = ref(parseInt(localStorage.getItem('oauth2-card-width') || '160'))
    const defaultIcon = ref(defaultIconSvg)

    const appList = computed(() => accessibleApps.value)

    // 估算每行能显示多少个卡片
    const estimatedCardsPerRow = computed(() => {
      const containerWidth = 1200 // 假设容器宽度
      const gap = 20
      return Math.floor((containerWidth + gap) / (cardWidth.value + gap))
    })

    const handleAppClick = async (app: OAuth2AppVO) => {
      if (app.status === 0) {
        ElMessage.warning('应用已被禁用')
        return
      }

      if (app.url) {
        if (app.url.startsWith('http')) {
          // 外部链接新窗口打开
          window.open(app.url, '_blank')
        } else {
          // 内部路由跳转
          // router.push(app.url)
          ElMessage.info('内部路由跳转功能待实现')
        }
      } else {
        ElMessage.warning('应用链接未配置')
      }
    }

    const loadOAuth2Apps = async () => {
      try {
        isLoading.value = true
        const response = await getUserAccessibleAppsApi()
        accessibleApps.value = response || []
        console.log('加载OAuth2应用成功:', accessibleApps.value)
      } catch (error) {
        console.error('加载OAuth2应用失败:', error)
        ElMessage.error('加载应用列表失败')
      } finally {
        isLoading.value = false
      }
    }

    const refreshApps = () => {
      loadOAuth2Apps()
    }

    const saveCardWidth = (width: number) => {
      localStorage.setItem('oauth2-card-width', width.toString())
    }

    onMounted(() => {
      loadOAuth2Apps()
    })

    return {
      isLoading,
      appList,
      showSettings,
      cardWidth,
      defaultIcon,
      estimatedCardsPerRow,
      handleAppClick,
      refreshApps,
      saveCardWidth
    }
  }
})
</script>

<style lang="scss" scoped>
.oauth2-apps {
  .box-card {
    .card-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      font-weight: bold;

      span {
        display: flex;
        align-items: center;
        gap: 8px;
        font-size: 16px;
        color: #303133;
      }

      .header-actions {
        display: flex;
        gap: 8px;
      }
    }
  }

  .settings-panel {
    margin-bottom: 20px;
    padding: 15px;
    background: #f8f9fa;
    border-radius: 8px;
    border: 1px solid #e9ecef;

    .setting-item {
      display: flex;
      align-items: center;
      margin-bottom: 10px;

      &:last-child {
        margin-bottom: 0;
      }

      span {
        font-size: 14px;
        color: #495057;
        font-weight: 500;
        min-width: 120px;
      }
    }
  }

  .loading-state,
  .empty-state {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 60px 20px;

    .el-icon {
      font-size: 48px;
      color: #909399;
      margin-bottom: 16px;
    }

    p {
      font-size: 16px;
      color: #606266;
      margin-bottom: 16px;
    }
  }

  .apps-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(var(--card-width, 160px), 1fr));
    gap: 20px;
    justify-content: start;
    align-items: start;
    width: 100%;
  }

  .app-item {
    // 宽度和高度通过内联样式动态设置
    padding: 16px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    background: #fff;
    border-radius: 12px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
    cursor: pointer;
    position: relative;
    overflow: hidden;

    &:hover {
      transform: translateY(-3px) scale(1.02);
      box-shadow: 0 8px 20px rgba(0, 0, 0, 0.15);
      z-index: 1;

      &::after {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 3px;
        background: #409EFF;
        animation: highlight 0.3s ease-out;
      }
    }

    &:active {
      transform: translateY(-1px) scale(0.98);
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    }

    .app-icon {
      margin-bottom: 12px;
      display: flex;
      justify-content: center;
      align-items: center;
      width: 100%;
      flex: 1;

      img {
        width: calc(var(--card-width, 160px) * 0.4);
        height: calc(var(--card-width, 160px) * 0.4);
        max-width: 64px;
        max-height: 64px;
        min-width: 32px;
        min-height: 32px;
        border-radius: 8px;
        object-fit: contain;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
      }
    }

    .app-info {
      width: 100%;
      text-align: center;
      flex-shrink: 0;

      .app-name {
        font-size: clamp(12px, calc(var(--card-width, 160px) * 0.08), 16px);
        font-weight: 500;
        margin-bottom: 6px;
        line-height: 1.3;
        max-height: calc(var(--card-width, 160px) * 0.2);
        overflow: hidden;
        text-overflow: ellipsis;
        display: -webkit-box;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
        color: #303133;
        word-break: break-word;
      }

      .app-description {
        font-size: clamp(10px, calc(var(--card-width, 160px) * 0.06), 12px);
        color: #909399;
        line-height: 1.4;
        max-height: calc(var(--card-width, 160px) * 0.15);
        overflow: hidden;
        text-overflow: ellipsis;
        display: -webkit-box;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
      }
    }

    .app-status {
      position: absolute;
      top: 8px;
      right: 8px;
      padding: 2px 6px;
      font-size: 10px;
      border-radius: 4px;

      &.disabled {
        background: #f56c6c;
        color: #fff;
      }
    }
  }

  // 响应式设计
  @media (max-width: 1200px) {
    .apps-grid {
      grid-template-columns: repeat(auto-fill, minmax(140px, 1fr));
    }
  }

  @media (max-width: 768px) {
    .apps-grid {
      grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
      gap: 16px;
    }

    .settings-panel {
      .setting-item {
        flex-direction: column;
        align-items: flex-start;
        gap: 8px;
      }
    }
  }

  @media (max-width: 480px) {
    .apps-grid {
      grid-template-columns: repeat(auto-fill, minmax(100px, 1fr));
      gap: 12px;
    }

    .settings-panel {
      display: none; // 在小屏幕上隐藏设置面板
    }
  }
}

@keyframes highlight {
  from {
    transform: scaleX(0);
    opacity: 0;
  }
  to {
    transform: scaleX(1);
    opacity: 1;
  }
}
</style>