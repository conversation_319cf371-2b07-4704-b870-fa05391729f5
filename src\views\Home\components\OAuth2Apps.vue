<template>
  <div class="oauth2-apps-container">
    <el-row :gutter="20">
      <el-col :span="24">
        <div class="section-title">
          <el-icon><Connection /></el-icon>
          统一门户
          <div class="title-actions">
            <el-button
              type="text"
              size="small"
              @click="showWidthController = !showWidthController"
            >
              {{ showWidthController ? '隐藏设置' : '显示设置' }}
            </el-button>
          </div>
        </div>
      </el-col>

      <!-- 加载中状态 -->
      <el-col :span="24" v-if="isLoading">
        <div class="loading-state">
          <el-icon class="is-loading"><Loading /></el-icon>
          <p>正在加载应用...</p>
        </div>
      </el-col>

      <!-- 空状态 -->
      <el-col :span="24" v-else-if="!hasApps">
        <div class="empty-state">
          <el-icon><InfoFilled /></el-icon>
          <p>暂无可用的集成应用</p>
          <!-- 调试信息 -->
          <div style="margin-top: 10px; font-size: 12px; color: #999;">
            <p>调试信息：</p>
            <p>isLoading: {{ isLoading }}</p>
            <p>hasApps: {{ hasApps }}</p>
            <p>appList.length: {{ appList.length }}</p>
            <p>accessibleApps: {{ JSON.stringify(appList) }}</p>
          </div>
        </div>
      </el-col>

      <!-- 应用列表 -->
      <el-col :span="24" v-if="!isLoading && hasApps">
        <div class="apps-container">
          <!-- 宽度调整控制器 -->
          <div v-if="showWidthController" class="width-controller">
            <div class="width-info">
              <span>卡片宽度: {{ cardWidth }}px</span>
              <span class="cards-per-row">预计每行: {{ estimatedCardsPerRow }} 个</span>
            </div>
            <el-slider
              v-model="cardWidth"
              :min="100"
              :max="200"
              :step="10"
              style="width: 200px; margin-left: 20px;"
              @change="saveCardWidth"
            />
          </div>

          <div class="apps-grid" :style="{ '--card-width': cardWidth + 'px' }">
            <div
              class="app-card"
              v-for="app in appList"
              :key="app.clientId"
              @click="handleAppClick(app)"
              :style="{ width: cardWidth + 'px', height: cardWidth + 'px' }"
            >
              <div class="app-icon">
                <img :src="app.logo || defaultIcon" :alt="app.name" />
              </div>
              <div class="app-info">
                <h3>{{ app.name }}</h3>
              </div>
            </div>
          </div>
        </div>
      </el-col>
    </el-row>
  </div>
</template>

<script lang="ts">
import { defineComponent, ref, onMounted, computed } from 'vue'
import { ElMessage } from 'element-plus'
import { Connection, Loading, InfoFilled } from '@element-plus/icons-vue'
import { getAccessibleOAuth2Apps, authorizeApp, type OAuth2ClientVO } from '@/api/system/oauth2/client'
import defaultIconSvg from '@/assets/svgs/icon.svg'

export default defineComponent({
  name: 'OAuth2Apps',
  components: {
    Connection,
    Loading,
    InfoFilled
  },
  setup() {
    const isLoading = ref(false)
    const accessibleApps = ref<OAuth2ClientVO[]>([])
    const cardWidth = ref(parseInt(localStorage.getItem('oauth2-card-width') || '140'))
    const defaultIcon = ref(defaultIconSvg)
    const showWidthController = ref(false)

    const appList = computed(() => accessibleApps.value)

    // 是否有可访问的应用
    const hasApps = computed(() => {
      return accessibleApps.value && accessibleApps.value.length > 0
    })

    // 估算每行能显示多少个卡片
    const estimatedCardsPerRow = computed(() => {
      const containerWidth = 1200 // 假设容器宽度
      const gap = 16
      return Math.floor((containerWidth + gap) / (cardWidth.value + gap))
    })

    const handleAppClick = async (app: OAuth2ClientVO) => {
      console.log('点击应用：', JSON.stringify(app))

      if (app.status === 1) {
        ElMessage.warning('应用已被禁用')
        return
      }

      if (!app.redirectUris || app.redirectUris.length === 0) {
        ElMessage.warning('应用未配置回调地址')
        return
      }

      try {
        const res = await authorizeApp(app.clientId, app.redirectUris[0])
        debugger
      
        if (res.data) {
          window.open(res.data, '_blank')
        } else {
          ElMessage.warning('授权失败：未获取到重定向地址')
        }
      } catch (error: any) {
        console.error('授权请求失败:', error)
        ElMessage.error(`授权失败: ${error.message || '未知错误'}`)
      }
    }

    const loadOAuth2Apps = async () => {
      try {
        console.log('开始加载OAuth2应用列表')
        isLoading.value = true
        const response = await getAccessibleOAuth2Apps()
        console.log('API响应:', response)
        console.log('response.data:', response.data)
        console.log('response 类型:', typeof response)
        console.log('response 是否为数组:', Array.isArray(response))

        // 根据实际响应结构处理数据
        if (Array.isArray(response)) {
          accessibleApps.value = response
        } else if (response && response.data && Array.isArray(response.data)) {
          accessibleApps.value = response.data
        } else if (response && Array.isArray(response)) {
          accessibleApps.value = response
        } else {
          accessibleApps.value = []
        }

        console.log('OAuth2应用列表加载结果:', accessibleApps.value)
        console.log('应用数量:', accessibleApps.value.length)
        console.log('hasApps 值:', hasApps.value)
      } catch (error: any) {
        console.error('加载OAuth2应用失败:', error)
        ElMessage.error('加载应用列表失败：' + (error.message || '未知错误'))
      } finally {
        isLoading.value = false
      }
    }

    const saveCardWidth = (width: number) => {
      localStorage.setItem('oauth2-card-width', width.toString())
    }

    onMounted(() => {
      loadOAuth2Apps()
    })

    return {
      isLoading,
      appList,
      hasApps,
      cardWidth,
      defaultIcon,
      showWidthController,
      estimatedCardsPerRow,
      handleAppClick,
      saveCardWidth
    }
  }
})
</script>

<style lang="scss" scoped>
.oauth2-apps-container {
  padding: 20px;

  .section-title {
    font-size: 18px;
    font-weight: bold;
    margin-bottom: 20px;
    color: #303133;
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: 8px;

    .title-actions {
      .el-button {
        font-size: 12px;
        padding: 4px 8px;
      }
    }
  }

  .loading-state,
  .empty-state {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 40px 0;

    .el-icon {
      font-size: 48px;
      color: #909399;
      margin-bottom: 16px;
    }

    p {
      font-size: 16px;
      color: #606266;
    }
  }

  .apps-container {
    width: 100%;
  }

  .width-controller {
    display: flex;
    align-items: center;
    margin-bottom: 20px;
    padding: 15px;
    background: #f8f9fa;
    border-radius: 8px;
    border: 1px solid #e9ecef;

    .width-info {
      display: flex;
      flex-direction: column;
      gap: 4px;

      span {
        font-size: 14px;
        color: #495057;
        font-weight: 500;
      }

      .cards-per-row {
        font-size: 12px;
        color: #6c757d;
        font-weight: 400;
      }
    }
  }

  .apps-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(var(--card-width, 140px), 1fr));
    gap: 16px;
    justify-content: start;
    align-items: start;
    width: 100%;
  }

  .app-card {
    // 宽度和高度通过内联样式动态设置
    padding: 12px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    background: #fff;
    border-radius: 12px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
    cursor: pointer;
    position: relative;
    overflow: hidden;

    &:hover {
      transform: translateY(-3px) scale(1.02);
      box-shadow: 0 8px 20px rgba(0, 0, 0, 0.15);
      z-index: 1;

      &::after {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 3px;
        background: #409EFF;
        animation: highlight 0.3s ease-out;
      }
    }

    &:active {
      transform: translateY(-1px) scale(0.98);
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    }

    .app-icon {
      margin-bottom: 8px;
      display: flex;
      justify-content: center;
      align-items: center;
      width: 100%;
      flex: 1;

      img {
        width: calc(var(--card-width, 140px) * 0.45);
        height: calc(var(--card-width, 140px) * 0.45);
        max-width: 80px;
        max-height: 80px;
        min-width: 40px;
        min-height: 40px;
        border-radius: 12px;
        object-fit: contain;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
      }
    }

    .app-info {
      width: 100%;
      text-align: center;
      flex-shrink: 0;

      h3 {
        font-size: clamp(10px, calc(var(--card-width, 140px) * 0.08), 14px);
        margin: 0;
        line-height: 1.3;
        max-height: calc(var(--card-width, 140px) * 0.25);
        overflow: hidden;
        text-overflow: ellipsis;
        display: -webkit-box;
        -webkit-line-clamp: 2;
        line-clamp: 2;
        -webkit-box-orient: vertical;
        color: #303133;
        word-break: break-word;
      }
    }
  }

  // 响应式设计
  @media (max-width: 1200px) {
    .apps-grid {
      grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
    }

    .width-controller {
      flex-direction: column;
      align-items: flex-start;
      gap: 10px;

      .width-info {
        flex-direction: row;
        gap: 20px;
      }

      .el-slider {
        width: 100% !important;
        max-width: 300px;
      }
    }
  }

  @media (max-width: 768px) {
    .apps-grid {
      grid-template-columns: repeat(auto-fill, minmax(100px, 1fr));
      gap: 12px;
      justify-content: center;
    }

    .width-controller {
      padding: 10px;

      .width-info {
        flex-direction: column;
        gap: 4px;

        span {
          font-size: 12px;
        }
      }
    }
  }

  @media (max-width: 480px) {
    .apps-grid {
      grid-template-columns: repeat(auto-fill, minmax(80px, 1fr));
      gap: 8px;
    }

    .width-controller {
      display: none; // 在小屏幕上隐藏宽度控制器
    }
  }
}

@keyframes highlight {
  from {
    transform: scaleX(0);
    opacity: 0;
  }
  to {
    transform: scaleX(1);
    opacity: 1;
  }
}
</style>