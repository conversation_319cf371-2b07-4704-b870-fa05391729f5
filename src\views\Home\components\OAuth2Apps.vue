<template>
  <div class="oauth2-apps">
    <el-card class="box-card">
      <template #header>
        <div class="card-header">
          <span>应用中心</span>
        </div>
      </template>
      
      <div v-loading="isLoading" class="apps-grid">
        <div 
          v-for="app in appList" 
          :key="app.id"
          class="app-item"
          @click="handleAppClick(app)"
        >
          <div class="app-icon">
            <img v-if="app.icon" :src="app.icon" :alt="app.name" />
            <el-icon v-else size="40"><Document /></el-icon>
          </div>
          <div class="app-name">{{ app.name }}</div>
          <div class="app-description">{{ app.description }}</div>
        </div>
      </div>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, computed } from 'vue'
import { ElCard, ElIcon, ElMessage } from 'element-plus'
import { Document } from '@element-plus/icons-vue'
import { getOAuth2AppsApi } from '@/api/oauth2'

interface OAuth2App {
  id: string
  name: string
  description?: string
  icon?: string
  url?: string
}

const isLoading = ref(false)
const accessibleApps = ref<OAuth2App[]>([])

const appList = computed(() => accessibleApps.value)

const handleAppClick = (app: OAuth2App) => {
  if (app.url) {
    if (app.url.startsWith('http')) {
      // 外部链接新窗口打开
      window.open(app.url, '_blank')
    } else {
      // 内部路由跳转
      // router.push(app.url)
    }
  } else {
    ElMessage.warning('应用链接未配置')
  }
}

const loadOAuth2Apps = async () => {
  try {
    isLoading.value = true
    const response = await getOAuth2AppsApi()
    accessibleApps.value = response.data || []
  } catch (error) {
    console.error('加载OAuth2应用失败:', error)
    ElMessage.error('加载应用列表失败')
  } finally {
    isLoading.value = false
  }
}

onMounted(() => {
  loadOAuth2Apps()
})
</script>

<style lang="scss" scoped>
.oauth2-apps {
  .box-card {
    .card-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      font-weight: bold;
    }
  }

  .apps-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    gap: 20px;
    min-height: 200px;
  }

  .app-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 20px;
    border: 1px solid #e4e7ed;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.3s;

    &:hover {
      border-color: #409eff;
      box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
      transform: translateY(-2px);
    }

    .app-icon {
      margin-bottom: 12px;
      
      img {
        width: 40px;
        height: 40px;
        object-fit: contain;
      }
    }

    .app-name {
      font-size: 16px;
      font-weight: 500;
      margin-bottom: 8px;
      text-align: center;
    }

    .app-description {
      font-size: 12px;
      color: #909399;
      text-align: center;
      line-height: 1.4;
    }
  }
}
</style>