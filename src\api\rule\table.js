import request from '@/utils/request'

// 获取表
export function getTable(id) {
  return request({
    url: `/metadata/table/${id}`,
    method: 'get'
  })
}

// 获取表列表(通过数据源ID)
export function getTableListBySourceId(sourceId) {
  return request({
    url: '/metadata/table/list-by-source',
    method: 'get',
    params: { sourceId }
  })
}

// 获取表分页
export function getTablePage(query) {
  return request({
    url: '/metadata/table/page',
    method: 'get',
    params: query
  })
} 