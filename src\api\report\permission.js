import request from '@/utils/request'

// 获取用户报表权限
export function getUserPermission(userId) {
  return request({
    url: '/report/permission/user/' + userId,
    method: 'get'
  })
}

// 更新用户报表权限
export function updateUserPermission(data) {
  return request({
    url: '/report/permission/user',
    method: 'put',
    data: data
  })
}

// 获取角色报表权限
export function getRolePermission(roleId) {
  return request({
    url: '/report/permission/role/' + roleId,
    method: 'get'
  })
}

// 更新角色报表权限
export function updateRolePermission(data) {
  return request({
    url: '/report/permission/role',
    method: 'put',
    data: data
  })
}

// 获取当前用户报表权限
export function getCurrentUserPermission() {
  return request({
    url: '/report/permission/current',
    method: 'get'
  })
}

// 校验报表权限
export function checkPermission(data) {
  return request({
    url: '/report/permission/check',
    method: 'post',
    data: data
  })
} 