export default {
  fu: {
    search_bar: {
      search: 'Search',
      adv_search: 'Advarequirednced search',
      ok: 'Confirm',
      cancel: 'Cancel',
      please_select: 'Please select',
      please_input: 'Please input',
      like: 'like ',
      not_like: 'not like',
      in: 'in',
      not_in: 'not in',
      gt: 'greater than',
      ge: 'Greater than or equal to',
      lt: 'less than',
      le: 'Less than or equal to',
      eq: 'equal to',
      ne: 'bot equal to',
      between: 'between',
      select_date: 'Select a date',
      start_date: 'Start date',
      end_date: 'End date',
      select_date_time: 'Select date time',
      start_date_time: 'Start date time',
      end_date_time: 'End date time',
      range_separator: 'To',
      data_time_error: 'The start date cannot be greater than the end date',
      clean: 'Clean',
      refresh: 'Refresh'
    },
    table: {
      ok: 'Confirm',
      custom_table_fields: 'Custom table fields',
      custom_table_fields_desc: 'Fixed field is not in the selection range'
    },
    steps: {
      cancel: 'Cancel',
      next: 'next',
      prev: 'Last step',
      finish: 'Finish'
    }
  },
  track: {
    upload_limit_format: 'The image format is incorrect. It supports JPG and PNG',
    upload_limit_size: 'Picture size shall not exceed'
  },
  route: {
    dashboard: 'Dashboard',
    documentation: 'Documentation',
    guide: 'Guide',
    permission: 'Permission',
    pagePermission: 'Page Permission',
    rolePermission: 'Role Permission',
    directivePermission: 'Directive Permission',
    icons: 'Icons',
    components: 'Components',
    tinymce: 'Tinymce',
    markdown: 'Markdown',
    jsonEditor: 'JSON Editor',
    dndList: 'Dnd List',
    splitPane: 'SplitPane',
    avatarUpload: 'Avatar Upload',
    dropzone: 'Dropzone',
    sticky: 'Sticky',
    countTo: 'Count To',
    componentMixin: 'Mixin',
    backToTop: 'Back To Top',
    dragDialog: 'Drag Dialog',
    dragSelect: 'Drag Select',
    dragKanban: 'Drag Kanban',
    charts: 'Charts',
    keyboardChart: 'Keyboard Chart',
    lineChart: 'Line Chart',
    mixChart: 'Mix Chart',
    example: 'Example',
    nested: 'Nested Routes',
    menu1: 'Menu 1',
    'menu1-1': 'Menu 1-1',
    'menu1-2': 'Menu 1-2',
    'menu1-2-1': 'Menu 1-2-1',
    'menu1-2-2': 'Menu 1-2-2',
    'menu1-3': 'Menu 1-3',
    menu2: 'Menu 2',
    Table: 'Table',
    dynamicTable: 'Dynamic Table',
    dragTable: 'Drag Table',
    inlineEditTable: 'Inline Edit',
    complexTable: 'Complex Table',
    tab: 'Tab',
    form: 'Form',
    createArticle: 'Create Article',
    editArticle: 'Edit Article',
    articleList: 'Article List',
    errorPages: 'Error Pages',
    page401: '401',
    page404: '404',
    errorLog: 'Error Log',
    excel: 'Excel',
    exportExcel: 'Export Excel',
    selectExcel: 'Export Selected',
    mergeHeader: 'Merge Header',
    uploadExcel: 'Upload Excel',
    zip: 'Zip',
    pdf: 'PDF',
    exportZip: 'Export Zip',
    theme: 'Theme',
    clipboardDemo: 'Clipboard',
    i18n: 'I18n',
    externalLink: 'External Link',
    profile: 'Profile'
  },
  navbar: {
    dashboard: 'Dashboard',
    github: 'Github',
    logOut: 'Log Out',
    profile: 'Profile',
    theme: 'Theme',
    size: 'Global Size'
  },
  login: {
    title: 'Login',
    welcome: 'Welcome To ',
    logIn: 'Login',
    username: 'Username',
    password: 'Password',
    any: 'any',
    thirdparty: 'Or connect with',
    thirdpartyTips: 'Can not be simulated on local, so please combine you own business simulation! ! !',
    expires: 'Login token expired, please login again',
    tokenError: 'Token error, please login again',
    username_error: 'Please enter the correct ID',
    password_error: 'The password can not be less than 8 digits',
    re_login: 'Login again',
    default_login: 'Normal'
  },
  commons: {
    consanguinity: 'Consanguinity',
    collapse_navigation: 'Collapse navigation',
    operate_cancelled: 'Operation cancelled',
    bind: 'Bind',
    unbind: 'Unbind',
    unlock: 'Unlock',
    unlock_success: 'Unlock success',
    parameter_effect: 'Parameter values only take effect when editing a dataset',
    uninstall: 'Uninstall',

    no_result: 'No Result',
    manage_member: 'Managing members',
    confirm_remove_cancel: 'Are you sure to delete the role?',
    user_confirm_remove_cancel: 'Are you sure you want to remove the user from the role?',
    default_value: 'Default Value',
    params_value: 'Param Value',
    input_role_name: 'Enter a role name',
    publish: 'publish',
    unpublished: 'Unpublished',
    default_pwd: 'Default Pwd',
    stop: 'Stop',
    first_login_tips: 'Please change the initial password',
    roger_that: 'Roger That',
    donot_noti: 'Do not tips',
    apply: 'Apply',
    search: 'Search',
    folder: 'Folder',
    no_target_permission: 'No permission',
    success: 'Success',
    switch_lang: 'Switch Language Success',
    close: 'Close',
    icon: 'Icon',
    all: 'All',
    enable: 'Enable',
    disable: 'Disable',
    yes: 'Yes',
    no: 'No',
    reset: 'Reset',
    catalogue: 'Catalogue',
    button: 'Button',
    gender: 'Gender',
    man: 'Man',
    woman: 'Woman',
    keep_secret: 'keep secret',
    nick_name: 'Name',
    confirmPassword: 'Confirm Password',
    upload: 'Upload',
    cover: 'Cover',
    not_cover: 'Not Cover',
    import_mode: 'Import Mode',
    import_module: 'Import module',
    please_fill_in_the_template: 'Please fill in the template',
    cut_back_old_version: 'Switch back to the old version',
    cut_back_new_version: 'Switch back to the new version',
    comment: 'Comments',
    examples: 'Examples',
    help_documentation: 'Help document',
    api_help_documentation: 'API document',
    delete_cancelled: 'Deletion canceled',
    workspace: 'Workspace',
    organization: 'Organization',
    menu: 'Menu',
    setting: 'Setting',
    project: 'Project',
    about_us: 'About Us',
    current_project: 'Current Project',
    name: 'Name',
    description: 'Description',
    annotation: 'Annotation',
    clear: 'Clean',
    save: 'Save',
    otherSave: 'Save as',
    update: 'Update',
    save_success: 'Saved successfully',
    delete_success: 'deleted successfully',
    copy_success: 'Copy succeeded',
    modify_success: 'Modified successfully',
    delete_cancel: 'Deletion canceled',
    confirm: 'Confirm',
    cancel: 'Cancel',
    prompt: 'Tips',
    operating: 'Operation',
    input_limit: 'The length is between {0} and {1} characters',
    login: 'Login',
    welcome: 'One stop open source data analysis platform',
    username: 'User Name',
    password: 'Pass word',
    input_username: 'Please enter the user name',
    input_password: 'Please input a password',
    test: 'Test',
    create_time: 'Create Time',
    update_time: 'Update Time',
    add: 'Add',
    member: 'Member',
    email: 'Email',
    phone: 'Phone',
    mobile_phone: 'Please enter your mobile phone number',
    mobile_phone_number: 'Phone number',
    role: 'Role',
    personal_info: 'Personal Info',
    api_keys: 'API Keys',
    quota: 'Quota',
    status: 'Status',
    show_all: 'Show All',
    show: 'Show',
    report: 'Report',
    user: 'User',
    system: 'System',
    personal_setting: 'Personal Setting',
    test_resource_pool: 'Test Resource Pool',
    system_setting: 'System Setting',
    input_content: 'Please input the content',
    create: 'Create',
    edit: 'Edit',
    copy: 'Copy',
    refresh: 'Refresh',
    remark: 'Remark',
    delete: 'Delete',
    reduction: 'Recovery',
    not_filled: 'Not Filled',
    please_select: 'Please Select',
    search_by_name: 'Search by name',
    personal_information: 'Personal Information',
    exit_system: 'Exit System',
    verification: 'Verification',
    title: 'Title',
    custom: 'Custom',
    select_date: 'Select Date',
    months_1: 'January',
    months_2: 'February',
    months_3: 'March',
    months_4: 'April',
    months_5: 'May',
    months_6: 'June',
    months_7: 'July',
    months_8: 'August',
    months_9: 'September',
    months_10: 'October',
    months_11: 'November',
    months_12: 'December',
    weeks_0: 'Sunday',
    weeks_1: 'Monday',
    weeks_2: 'Tuesday',
    weeks_3: 'Wednesday',
    weeks_4: 'Thursday',
    weeks_5: 'Friday',
    weeks_6: 'Saturday',
    system_parameter_setting: 'System Parameter Setting',
    connection_successful: 'Connection Successful',
    connection_failed: 'Connection Failed',
    save_failed: 'Save Failed',
    host_cannot_be_empty: 'Host cannot be empty',
    port_cannot_be_empty: 'Port cannot be empty',
    account_cannot_be_empty: 'Account cannot be empty',
    remove: 'Remove',
    remove_cancel: 'Remove Cancel',
    remove_success: 'Remove Success',
    tips: 'The authentication information has expired. Please login again.',
    not_performed_yet: 'Not yet implemented',
    incorrect_input: 'Incorrect Input',
    delete_confirm: 'Please enter the following to confirm the deletion:',
    login_username: 'ID or Email',
    input_login_username: 'Please enter user ID or email',
    input_name: 'Please enter user name',
    please_upload: 'Please upload the file',
    please_fill_path: 'Please fill in the URL path',
    formatErr: 'Format Error',
    please_save: 'Please Save',
    reference_documentation: 'Reference documents',
    id: 'ID',
    millisecond: 'Millisecond',
    cannot_be_null: 'Cannot be null',
    required: 'Required',
    already_exists: 'Already Exists',
    modifier: 'Modifier',
    validate: 'Validate',
    batch_add: 'Batch Add',
    tag_tip: 'Enter add label',
    search_keywords: 'Enter keywords to search',
    table: {
      select_tip: '{0} data selected'
    },
    date: {
      select_date: 'Select Date',
      start_date: 'Start Date',
      end_date: 'End Date',
      select_date_time: 'Select date time',
      start_date_time: 'Start date time',
      end_date_time: 'End date time',
      range_separator: 'to',
      data_time_error: 'The start date cannot be greater than the end date.',
      one_day: 'One day',
      one_week: 'One week',
      one_month: 'One month'
    },
    adv_search: {
      title: 'Advanced search',
      combine: 'Combined query',
      test: 'Test',
      project: 'Project',
      search: 'Search',
      reset: 'Reset',
      and: 'All',
      or: 'Any one',
      operators: {
        is_empty: 'Empty',
        is_not_empty: 'Not empty',
        like: 'Contain',
        not_like: 'Not Contain',
        in: 'In',
        not_in: 'Not in',
        gt: 'Greater Than',
        ge: 'Greater than or equal to',
        lt: 'Less Than',
        le: 'Less than or equal to',
        equals: 'equals',
        not_equals: 'Not equals',
        between: 'between',
        current_user: 'Current User'
      },
      message_box: {
        alert: 'Alert',
        confirm: 'Confirm'
      }
    },
    monitor: 'Monitor',
    image: 'Image',
    tag: 'Tag',
    module: {
      select_module: 'Select Module',
      default_module: 'Default Module'
    },
    datasource: 'Datasource',
    char_can_not_more_50: 'Can not more 50 char',
    share_success: 'Share Success',
    input_id: 'Please input ID',
    input_pwd: 'Please input password',
    message_box: {
      alert: 'Alert',
      confirm: 'Confirm',
      ok: 'Confirm',
      cancel: 'Cancel'
    },
    ukey_title: 'API Keys',
    thumbnail: 'thumbnail',
    confirm_delete: 'Confirm delete',
    delete_this_dashboard: 'Are you sure to delete this dashboard?',
    confirm_stop: 'Confirm stop',
    stop_success: 'Stop success',
    treeselect: {
      no_children_text: 'No sub-options.',
      no_options_text: 'No options available.',
      no_results_text: 'No results found...'
    }
  },
  documentation: {
    documentation: 'Documentation',
    github: 'Github Repository'
  },
  permission: {
    addRole: 'New Role',
    editPermission: 'Edit',
    roles: 'Your roles',
    switchRoles: 'Switch roles',
    tips: 'In some cases, using v-permission will have no effect. For example: Element-UI  el-tab or el-table-column and other scenes that dynamically render dom. You can only do this with v-if.',
    delete: 'Delete',
    confirm: 'Confirm',
    cancel: 'Cancel'
  },
  guide: {
    description: 'The guide page is useful for some people who entered the project for the first time. You can briefly introduce the features of the project. Demo is based on ',
    button: 'Show Guide'
  },
  components: {
    documentation: 'Documentation',
    tinymceTips: 'Rich text is a core feature of the management backend, but at the same time it is a place with lots of pits. In the process of selecting rich texts, I also took a lot of detours. The common rich texts on the market have been basically used, and I finally chose Tinymce. See the more detailed rich text comparison and introduction.',
    dropzoneTips: 'Because my business has special needs, and has to upload images to qiniu, so instead of a third party, I chose encapsulate it by myself. It is very simple, you can see the detail code in @/components/Dropzone.',
    stickyTips: 'when the page is scrolled to the preset position will be sticky on the top.',
    backToTopTips1: 'When the page is scrolled to the specified position, the Back to Top button appears in the lower right corner',
    backToTopTips2: 'You can customize the style of the button, show / hide, height of appearance, height of the return. If you need a text prompt, you can use element-ui el-tooltip elements externally',
    imageUploadTips: 'Since I was using only the vue@1 version, and it is not compatible with mockjs at the moment, I modified it myself, and if you are going to use it, it is better to use official version.',
    run_once: 'Run once',
    continue: 'continue',
    hour: 'hour',
    minute: 'minute',
    day: 'day',
    by_task_name: 'Search by task name',
    message_receiving_management: 'Message receiving management',
    upload_failed: 'Upload failed',
    is_not_supported: 'The file format is not supported',
    is_too_large: 'The file is too large',
    enter_kettle_address: 'Please enter kettle address',
    enter_the_port: 'Please enter the port',
    one_user_name: 'enter one user name',
    input_a_password: 'Please input a password',
    address_is_required: 'Kettle address is required',
    port_is_required: 'Port is required',
    name_is_required: 'User name is required',
    password_is_required: 'Password is required',
    help_document_link: 'Invalid help document link',
    such_as_dataeasedataease: 'Please enter the login page title, such as: DataEase',
    for_example_dataease: 'Please enter the system name, for example: DataEase',
    time_is_required: 'Start time is required',
    delete_this_task: 'Are you sure to delete this task?',
    it_takes_effect: 'Uninstall and restart the server before it takes effect',
    uninstall_the_plugin: 'Are you sure to uninstall the plug-in?',
    uninstall: 'uninstall',
    relevant_content_found: 'No relevant content found',
    view_tasks: 'View tasks',
    no_classification: 'No classification',
    no_template: 'No template',
    data_set_required: 'Data set (required)',
    unread_message: 'Unread message',
    read_message: 'Read message',
    all_messages: 'All messages',
    message_list: 'Message list',
    all_read_successfully: 'All read successfully',
    by_plugin_name: 'Search by plug-in name',
    unable_to_uninstall: 'Built in plug-in, unable to uninstall',
    unable_to_update: 'Built in plug-in, unable to update',
    free: 'free',
    cost: 'cost',
    developer: 'developer',
    edition: 'edition',
    installation_time: 'Install time'
  },
  table: {
    dynamicTips1: 'Fixed header, sorted by header order',
    dynamicTips2: 'Not fixed header, sorted by click order',
    dragTips1: 'The default order',
    dragTips2: 'The after dragging order',
    title: 'Title',
    importance: 'Imp',
    type: 'Type',
    remark: 'Remark',
    search: 'Search',
    add: 'Add',
    export: 'Export',
    reviewer: 'reviewer',
    id: 'ID',
    date: 'Date',
    author: 'Author',
    readings: 'Readings',
    status: 'Status',
    actions: 'Actions',
    edit: 'Edit',
    publish: 'Publish',
    draft: 'Draft',
    delete: 'Delete',
    cancel: 'Cancel',
    confirm: 'Confirm'
  },
  deDataset: {
    search_by_name: 'Search by name',
    new_folder: 'New Folder',
    search_fields: 'Search Fields',
    show_rows: 'Show Rows',
    display: 'Display',
    row: 'Row',
    restricted_objects: 'Restricted Objects',
    select_data_source: 'Select Data Source',
    by_table_name: 'Search by table name',
    run_a_query: 'Run a query',
    running_results: 'Running results',
    parameter_type: 'Parameter Type',
    run_failed: 'Run failed',
    select_data_table: 'Select Data Table',
    in_the_file: 'Merged cells cannot exist in the file',
    or_date_type: 'The first line of the file is the header line, which cannot be empty or date type',
    is_within_500m: 'Please ensure the size of Excel file is within 500M',
    upload_data: 'Upload data',
    excel_data_first: 'Please upload Excel data first',
    is_currently_available: 'No data table is currently available',
    sure_to_synchronize: 'Synchronizing fields may cause changes to the edited fields. Are you sure to synchronize?',
    folder_name: 'Folder Name',
    folder: 'Folder',
    edit_folder: 'Edit Folder',
    name_already_exists: 'Folder name already exists',
    data_preview: 'Data preview',
    original_name: 'Original Name',
    database: 'Database',
    selected: 'Selected:',
    table: 'Table',
    no_dataset_click: 'No dataset, click',
    create: 'Create',
    new_folder_first: 'Please create a new folder first',
    on_the_left: 'Please select a dataset on the left',
    expression_syntax_error: 'Field expression syntax error',
    create_dashboard: 'Create Dashboard',
    cannot_be_empty: 'SQL cannot be empty',
    data_reference: 'Data Reference',
    want_to_replace: 'Replacement may affect custom dataset, associated dataset, dashboard, etc. Do you want to replace?',
    replace_the_data: 'Are you sure to replace the data?',
    append_successfully: 'Append successfully',
    already_exists: 'Dataset name already exists',
    edit_dataset: 'Edit Dataset',
    convert_to_indicator: 'Convert to Indicator',
    convert_to_dimension: 'Convert to Dimension',
    left_to_edit: 'Select the data table on the left to edit',
    cannot_be_duplicate: 'The dataset name cannot be duplicate',
    set_saved_successfully: 'Data set saved successfully',
    to_start_using: 'Browse the contents of your database, tables and columns. Choose a database to get started.',
    to_run_query: 'Click to run query',
    the_running_results: 'You can view the running results',
    item: 'item',
    logic_filter: 'Condition Filter',
    enum_filter: 'Enum Filter'
  },
  detabs: {
    custom_sort: 'Custom Sort',
    eidttitle: 'Edit Title',
    selectview: 'Select View',
    selectOthers: 'Select Others',
    availableComponents: 'Available Components',
    please: 'Please',
    head_position: 'Head Position'
  },
  example: {
    warning: 'Creating and editing pages cannot be cached by keep-alive because keep-alive include does not currently support caching based on routes, so it is currently cached based on component name. If you want to achieve a similar caching effect, you can use a browser caching scheme such as localStorage. Or do not use keep-alive include to cache all pages directly. See details'
  },
  errorLog: {
    tips: 'Please click the bug icon in the upper right corner',
    description: 'Now the management system are basically the form of the spa, it enhances the user experience, but it also increases the possibility of page problems, a small negligence may lead to the entire page deadlock. Fortunately Vue provides a way to catch handling exceptions, where you can handle errors or report exceptions.',
    documentation: 'Document introduction'
  },
  excel: {
    export: 'Export',
    selectedExport: 'Export Selected Items',
    placeholder: 'Please enter the file name (default excel-list)'
  },
  zip: {
    export: 'Export',
    placeholder: 'Please enter the file name (default file)'
  },
  pdf: {
    tips: 'Here we use window.print() to implement the feature of downloading PDF.'
  },
  theme: {
    change: 'Change Theme',
    documentation: 'Theme documentation',
    tips: 'Tips: It is different from the theme-pick on the navbar is two different skinning methods, each with different application scenarios. Refer to the documentation for details.',
    base: 'Base color',
    font: 'Font color',
    border: 'Border color',
    background: 'Background color',
    custom: 'Custom color',
    otherSave: 'Theme Save as',
    info: 'Theme info',
    add: 'Add Theme',
    please_input_name: 'Please enter a name',
    name_repeat: 'Name already exists'
  },
  tagsView: {
    refresh: 'Refresh',
    close: 'Close',
    closeOthers: 'Close Others',
    closeAll: 'Close All'
  },
  settings: {
    title: 'Page style setting',
    theme: 'Theme Color',
    tagsView: 'Open Tags-View',
    fixedHeader: 'Fixed Header',
    sidebarLogo: 'Sidebar Logo'
  },
  sysParams: {
    display: 'Display Setting',
    ldap: 'LDAP Setting',
    oidc: 'OIDC Setting',
    theme: 'Theme Setting',
    cas: 'CAS Setting',
    map: 'MAP Setting'
  },
  license: {
    i18n_no_license_record: 'No License Record',
    i18n_license_is_empty: 'License is empty.',
    title: 'Authorization Management',
    corporation: 'Customer Name',
    time: 'Authorization Time',
    product: 'Product',
    edition: 'Edition',
    licenseVersion: 'Authorized Version',
    count: 'Authorized Count',
    valid_license: 'Valid License',
    show_license: 'Show License',
    valid_license_error: 'Verification failed',
    status: 'Authorization status',
    valid: 'Valid',
    invalid: 'Invalid',
    expired: 'Expired'
  },
  member: {
    create: 'Add members',
    modify: 'Modify members',
    delete_confirm: 'Confirm to delete this user?',
    please_choose_member: 'Please choose member',
    search_by_name: 'Search by name',
    modify_personal_info: 'Modify personal info',
    edit_password: 'Reset Password',
    edit_information: 'Edit Information',
    input_name: 'Input Name',
    input_email: 'Input Email',
    special_characters_are_not_supported: 'Special characters are not supported',
    mobile_number_format_is_incorrect: 'Incorrect format of mobile phone number',
    email_format_is_incorrect: 'The mailbox format is incorrect',
    password_format_is_incorrect: 'Valid password: 8-30 digits, English upper and lower case letters + numbers + special characters (optional)',
    old_password: 'Old Password',
    new_password: 'New Password',
    repeat_password: 'Confirm Password',
    inconsistent_passwords: 'The two passwords are inconsistent',
    remove_member: 'Confirm to remove the member',
    org_remove_member: 'Removing the user from the organization will remove the permissions of all workspaces in the organization. Confirm to remove the member?',
    input_id_or_email: 'Please enter user ID or user email',
    no_such_user: 'No such user information, please enter the correct user ID or user email.'
  },
  user: {
    create: 'Create User',
    modify: 'Modify User',
    input_name: 'Please enter user name',
    input_id: 'Please enter user ID',
    id_mandatory: 'id is mandatory',
    name_mandatory: 'name is mandatory',
    email_mandatory: 'email is mandatory',
    role_mandatory: 'role is mandatory',
    phone_format: 'mobile phone number format is incorrect',
    select_gender: 'Please select gender',
    input_email: 'Please input email',
    input_password: 'Please input a password',
    input_phone: 'Please enter the phone number',
    input_roles: 'Please select role',
    select_users: 'Please select user',
    user_name_pattern_error: 'IDs can only contain alphanumeric and ._- and start with a letter!',
    special_characters_are_not_supported: 'Special characters are not supported',
    mobile_number_format_is_incorrect: 'Incorrect format of mobile phone number',
    email_format_is_incorrect: 'The mailbox format is incorrect',
    delete_confirm: 'Confirm to delete this user?',
    apikey_delete_confirm: 'Confirm to delete this API key?',
    input_id_placeholder: 'Please enter ID (Chinese is not supported)',
    source: 'User Source',
    choose_org: 'Please select a role',
    choose_role: '请选择角色',
    reset_password: 'Reset Password',
    current_user: 'Current User',
    origin_passwd: 'Origin Password',
    new_passwd: 'New Password',
    confirm_passwd: 'Confirm Password',
    change_password: 'Change Password',
    search_by_name: 'Search by name',
    import_ldap: 'Import LDAP users',
    result_one: 'Results',
    clear_filter: 'Empty condition',
    recover_pwd: 'Restore to the original password?',
    filter_method: 'Filter criteria',
    filter: 'Screen',
    list: 'List item',
    list_info: 'Please select the information to be displayed in the list',
    sure_delete: 'Are you sure to delete this user?',
    wecom_id: 'Wecom Account',
    dingtalk_id: 'Dingtalk Account',
    lark_id: 'Lark Account',
    input_wecom_id: 'Please input wecom account',
    input_dingtalk_id: 'Please input dingtalk account',
    input_lark_id: 'Please input lark account'
  },
  ldap: {
    url: 'LDAP url',
    dn: 'LDAP DN',
    password: 'Password',
    ou: 'OU',
    filter: 'filter',
    mapping: 'LDAP mapping',
    open: 'Enable LDAP Authentication',
    input_url: 'Please key LDAP url',
    input_dn: 'Please key DN',
    input_password: 'Please key password',
    input_ou: 'Please key OU',
    input_filter: 'Please key filter',
    input_mapping: 'Please key LDAP mapping',
    input_username: 'Please key username',
    input_url_placeholder: 'Please key url (like ldap://localhost:389)',
    input_ou_placeholder: 'Please key OU ',
    input_filter_placeholder: 'Please key filter',
    input_mapping_placeholder: 'like：{"userName":"uid","nickName":"cn","email":"mail"}',
    test_connect: 'Test connect',
    edit: 'Edit',
    login_success: 'Login success',
    url_cannot_be_empty: 'LDAP url can not be empty',
    dn_cannot_be_empty: 'LDAP DN can not be empty',
    ou_cannot_be_empty: 'LDAP OU can not be empty',
    filter_cannot_be_empty: 'LDAP filter can not be empty',
    mapping_cannot_be_empty: 'LDAP mapping can not be empty',
    password_cannot_be_empty: 'LDAP password can not be empty',
    import_ldap: 'Import LDAP User'
  },
  oidc: {
    auth_endpoint: 'Please key AuthEndpoint',
    token_endpoint: 'Please key TokenEndpoint',
    userinfo_endpoint: 'Please key UserinfoEndpoint',
    logout_endpoint: 'Please key logoutEndpoint',
    clientId: 'Please key ClientId',
    secret: 'Please key Secret',
    scope: 'Please key scope',
    redirectUrl: 'Please key redirectUrl',
    input_mapping: 'Please key OIDC mapping',
    open: 'Enable OIDC Authentication'
  },
  role: {
    menu_authorization: 'Menu Authorization',
    data_authorization: 'Data Authorization',
    please_choose_role: 'Please select role',
    admin: 'Administrator',
    org_admin: 'Organization Administrator',
    org_member: 'Organization Member',
    add: 'Create Role',
    delete: 'Delete Role',
    modify: 'Modify Role',
    tips: 'Tips',
    confirm_delete: 'Confirm delete role ',
    role_name: 'Role Name ',
    search_by_name: 'Search by name',
    pls_input_name: 'please input name',
    search_by_name_email: 'Search by name or email',
    api_role: 'API role',
    role_exist: 'Failed to add, the role already exists',
    add_api_role: 'Add API role',
    can_not_move: `Can't be removed, keep at least one administrator`,
    manage_can_not_move: 'Administrator is a preset role of the system. By default, he has all the permissions of system management and cannot be deleted',
    manage_can_not_update: 'Administrator is a preset role of the system. By default, he has all the permissions of system management and cannot be edit',
    role_description: 'Role description',
    editer_role: 'Edit role',
    add_role: 'Add role',
    role_name_exist: 'The role name already exists',
    search_by_role: 'Search by role name'
  },
  menu: {
    parent_category: 'Parent Category',
    module_name: 'Module Name',
    module_path: 'Module Path',
    route_addr: 'Route Address',
    menu_sort: 'Menu Sort',
    authority_identification: 'Authority Identification',
    button_name: 'Button Name',
    select_icon: 'Click the select icon',
    create_time: 'Create Time',
    tile: 'Menu Title',
    create: 'Create Menu',
    modify: 'Modify Menu',
    delete: 'Delete Menu',
    delete_confirm: 'Confirm to delete Menu?',
    menu_type: 'Menu Type'
  },
  organization: {
    parent_org: 'Parent Organization',
    select_parent_org: 'Select parent organization',
    top_org: 'Top Organization',
    name: 'Name',
    sort: 'Sort',
    sub_organizations: 'Sub Organizations',
    create_time: 'Create Time',
    create: 'Create',
    modify: 'Modify',
    delete: 'Delete',
    delete_confirm: 'Are you sure you want to delete the organization?',
    input_name: 'Please enter name',
    select_organization: 'Please select organization',
    search_by_name: 'Search by name',
    special_characters_are_not_supported: 'Format error (special characters are not supported and cannot start and end with \'-\')',
    select: 'Select organization',
    member: 'Member',
    organization: 'Organization',
    add_user: 'Add user',
    sure_move_user: 'Are you sure to remove this user from the organization?',
    move_success: 'Removed successfully',
    user: 'user',
    add_organization: 'Add organization',
    default_organization_cannot_move: 'The default organization cannot be deleted',
    organization_name: 'Organization name',
    input_organization_name: 'Please enter the organization name',
    relate_top_organization: 'Associated parent organization',
    organization_name_exist: 'Organization name already exists',
    cannot_delete: 'Cannot delete',
    remove_user_first: 'Please remove all users and child nodes in the organization before deleting the organization',
    sure_delete_organization: 'Are you sure to delete this organization?',
    add_child_org: 'Add sub organization',
    edite_organization: 'Edit organization'
  },
  system_parameter_setting: {
    email_server_config: 'Mailbox server configuration',
    edit_success: 'Edit success',
    mailbox_service_settings: 'Mail Setting',
    test_connection: 'Test connection',
    SMTP_host: 'SMTP Host',
    basic_setting: 'Basic Setting',
    front_time_out: 'Request timeOut(unit: second, Attention: Refresh browser takes effect after saving)',
    msg_time_out: 'Message retention time(unit: day)',
    login_type: 'Default login type',
    empty_front: 'If empty then default value is 100s',
    empty_msg: 'If empty then default value is 30 days',
    front_error: 'Valid range [0 - 300]', // 修改了提示信息
    msg_error: 'Valid range [1 - 365]',
    limit_times_error: 'Valid range [1 - 100]',
    relieve_times_error: 'Valid range [1 - 100]',
    SMTP_port: 'SMTP Port',
    SMTP_account: 'SMTP Account',
    SMTP_password: 'SMTP Password',
    SSL: 'Turn on SSL (if the SMTP port is 465, you usually need to enable SSL)',
    TLS: 'Turn on TLS (if the SMTP port is 587, you usually need to enable TLS)',
    SMTP: 'Secret free SMTP',
    host: 'Host number cannot be empty',
    port: 'Port number cannot be empty',
    account: 'Account cannot be empty',
    test_recipients: 'Test recipients',
    tip: 'Tip: use as test mail recipient only',
    engine_mode_setting: 'Engine Setting',
    kettle_setting: 'Kettle Setting',
    cas_selected_warn: 'Selecting CAS will cause you to login again',
    cas_reset: 'CAS switches back to the default login mode to access API:',
    main_color: 'Main color',
    success_color: 'Success color',
    warning_color: 'Warning color:',
    wrong_color: 'Wrong color',
    main_text_color: 'Main text color',
    secondary_font_color: 'Secondary font color',
    prompt_font_color: 'Prompt font color',
    disable_font_color: 'Disable font color',
    component_stroke_color: 'Component stroke color',
    card_stroke_color: 'Card stroke color',
    basic_white: 'Basic white',
    page_background_color: 'Page background color',
    disable_background_color: 'Input box: disable background color',
    basic_black: 'Basic black',
    label_color: 'Label color:',
    system_theme: 'System theme',
    custom_theme: 'Custom theme',
    color_settings: 'Color settings',
    add_theme: 'Add theme',
    subject_based: 'Subject based',
    the_subject_name: 'Please enter the subject name',
    name_already_exists: 'The subject name already exists',
    successfully_and_apply: 'Save successfully and apply',
    sure_to_exit: 'The information you filled in has not been saved. Are you sure to exit?',
    copy_theme: 'Copy theme',
    advanced_configuration: 'Advanced configuration',
    no_custom_theme: 'No custom theme',
    recommended_size: 'Recommended size:',
    support: 'support',
    no_more_than: 'Size no more than',
    request_timeout: 'Request timeout',
    message_retention_time: 'Message retention time',
    log_retention_time: 'Log retention time',
    ds_check_time: 'Data source detection interval',
    test_mail_recipient: 'Used only as a test mail recipient',
    to_enable_tsl: 'If the SMTP port is 587, you usually need to enable TSL',
    to_enable_ssl: 'If the SMTP port is 465, you usually need to enable SSL',
    added_successfully: 'Added successfully',
    text_link_etc: 'Applicable scenarios: call to action, selected status, information highlight, general prompt information, text link, etc',
    prompt_and_icon: 'Applicable scenario: success status prompt and Icon',
    prompt_and_icon_danger: 'Applicable scenario: warning status prompt and Icon',
    icon_danger_button: 'Applicable scenario: error status prompt and icon, danger button',
    first_level_icon: 'Applicable scenario: first level title, first level text, important information display, first level Icon',
    copy_secondary_icon: 'Applicable scenario: secondary title, secondary copy, secondary Icon',
    radio_checkbox_unchecked: 'Applicable scenario: input box guide, auxiliary copy, prompt copy, three-level icon, radio, checkbox unchecked',
    button_background_color: 'Applicable scenario: disable copy, disable icon, disable button background color',
    background_header_background: 'Applicable scenario: page background, header background',
    scenario_component_stroking: 'Applicable scenario: component stroking',
    main_background: 'Main background',
    content_background: 'Content background',
    select_font: 'Select font',
    no_font_selected: 'No font selected',
    head_background: 'Head background',
    head_font: 'Head font',
    menu_floating_background: 'Menu floating background',
    menu_selection_background: 'Menu selection background',
    left_menu_background: 'Left menu background',
    left_menu_font: 'Left menu font',
    table_background: 'Table background',
    table_font: 'Table font',
    table_borders: 'Table borders',
    subject_name: 'Subject name',
    template_name: 'Template name',
    search_keywords: 'Search keywords:',
    delete_this_topic: 'Are you sure to delete this topic?',
    network_error: 'network error',
    to_overwrite_them: 'There are templates with the same name in the current classification. Do you want to overwrite them?',
    import_succeeded: 'Import succeeded',
    name_already_exists_type: 'Classification name already exists',
    rename_succeeded: 'Rename succeeded',
    the_same_category: 'The template name already exists under the same category',
    delete_this_template: 'Are you sure to delete this template?',
    also_be_deleted: 'After deletion, all templates in this category will also be deleted.',
    delete_this_category: 'Are you sure to delete this category?',
    edit_template: 'Edit template',
    edit_classification: 'Edit classification',
    classification_name: 'Classification Name:',
    by_event_details: 'Search by event details',
    password_input_error: 'Original password input error'
  },
  chart: {
    chart_refresh_tips: 'View refresh setting takes precedence over panel refresh setting',
    '1-trend': 'trend',
    '2-state': 'State',
    '3-rank': 'Rank',
    '4-location': 'Location',
    '5-weather': 'Weather',
    chinese: 'Chinese',
    mark_field: 'Field',
    mark_value: 'Value',
    function_style: 'Function style',
    condition_style: 'Mark style',
    longitude: 'Longitude',
    latitude: 'Latitude',
    gradient: 'Gradient',
    layer_controller: 'Quota switch',
    suspension: 'Suspension',
    chart_background: 'Component background',
    date_format: 'Select date resolution format',
    solid_color: 'Solid color',
    split_gradient: 'Split gradient',
    continuous_gradient: 'Continuous gradient',
    map_center_lost: 'The graph is missing the centroid or center attribute, please complete it and try again',
    margin_model: 'Model',
    margin_model_auto: 'Auto',
    margin_model_absolute: 'Absolute',
    margin_model_relative: 'Relative',
    margin_placeholder: 'Please enter a number from 0-100',
    margin_absolute_placeholder: 'Please enter a number from 0-40',
    rich_text_view_result_tips: 'The rich text view selects only the first result',
    rich_text_view: 'Rich Text View',
    view_reset: 'View Reset',
    view_reset_tips: 'Discard Changes To View?',
    export_img: 'Export Img',
    title_repeat: 'The Title Already Exist',
    save_snapshot: 'Save Snapshot',
    datalist: 'Chart',
    add_group: 'Add Group',
    add_scene: 'Add Scene',
    group: 'Group',
    scene: 'Scene',
    delete: 'Delete',
    move_to: 'Move',
    rename: 'Rename',
    tips: 'Tips',
    confirm_delete: 'Confirm Delete',
    delete_success: 'Delete Success',
    confirm: 'Confirm',
    cancel: 'Cancel',
    search: 'Search',
    back: 'Back',
    add_table: 'Add Dataset',
    process: 'Speed of progress',
    add_chart: 'Add Chart',
    db_data: 'Database Dataset',
    sql_data: 'SQL Dataset',
    excel_data: 'Excel Dataset',
    custom_data: 'Custom Dataset',
    pls_slc_tbl_left: 'Please select the chart from the left',
    add_db_table: 'Add Database Dataset',
    pls_slc_data_source: 'Please select data source',
    table: 'Table',
    edit: 'Edit',
    create_view: 'Create Chart',
    data_preview: 'Data preview',
    dimension: 'Dimension',
    quota: 'Quota',
    title: 'Title',
    show: 'Show',
    chart_type: 'Chart Type',
    shape_attr: 'Attribute',
    module_style: 'Style',
    result_filter: 'Filters',
    x_axis: 'Horizontal axis',
    y_axis: 'Longitudinal axis',
    chart: 'Chart',
    close: 'Close',
    summary: 'Summary Method',
    fast_calc: 'Fast Calculation',
    sum: 'Sum',
    count: 'Count',
    avg: 'Avg',
    max: 'Max',
    min: 'Min',
    stddev_pop: 'Standard Deviation',
    var_pop: 'Variance',
    quick_calc: 'Fast calculation',
    show_name_set: 'Display name setting',
    show_name: 'Display name',
    color: 'Color',
    color_case: 'Color Scheme',
    pls_slc_color_case: 'Please choose a color scheme',
    color_default: 'Default',
    color_retro: 'Retro',
    color_future: 'Future',
    color_gradual: 'Gradual',
    color_business: 'Business',
    color_gentle: 'Gentle',
    color_elegant: 'Elegant',
    color_technology: 'Technology',
    color_simple: 'Simple',
    not_alpha: 'Opacity',
    area_border_color: 'Map border',
    area_base_color: 'Base map',
    size: 'Size',
    bar_width: 'Column Width',
    bar_gap: 'Column septum',
    adapt: 'Self-adaption',
    line_width: 'line_width',
    line_type: 'line_type',
    line_symbol: 'Break point',
    line_symbol_size: 'Break point size',
    line_type_solid: 'Solid line',
    line_type_dashed: 'Dotted line',
    line_symbol_circle: 'Circular',
    line_symbol_emptyCircle: 'Hollow circle',
    line_symbol_rect: 'Rectangle',
    line_symbol_roundRect: 'Rounded rectangle',
    line_symbol_triangle: 'Triangle',
    line_symbol_diamond: 'Diamond',
    line_symbol_pin: 'nail',
    line_symbol_arrow: 'arrow',
    line_symbol_none: 'None',
    line_area: 'area',
    pie_inner_radius: 'inner_radius',
    pie_outer_radius: 'outer_radius',
    funnel_width: 'width',
    line_smooth: 'Smooth polyline',
    title_style: 'Title Style',
    text_fontsize: 'Font size',
    text_color: 'Font color',
    text_h_position: 'Horizontal position',
    text_v_position: 'Vertical position',
    text_pos_left: 'Left',
    text_pos_center: 'Center',
    text_pos_right: 'Right',
    text_pos_top: 'Top',
    text_pos_bottom: 'Bottom',
    text_italic: 'italic',
    italic: 'italic',
    orient: 'direction',
    horizontal: 'horizontal',
    vertical: 'vertical',
    legend: 'legend',
    shape: 'shape',
    polygon: 'polygon',
    circle: 'circular',
    label: 'Label',
    label_position: 'Label location',
    label_bg: 'Label BG',
    label_shadow: 'Label Shadow',
    label_shadow_color: 'Shadow Color',
    content_formatter: 'Content Format',
    label_reserve_decimal_count: 'Reserve Decimal',
    inside: 'Inside',
    tooltip: 'Tips',
    tooltip_item: 'Data Item',
    tooltip_axis: 'Coordinate Axis',
    formatter_plc: 'When the content format is empty, the default format is displayed',
    xAxis: 'Horizontal axis',
    yAxis: 'Longitudinal axis',
    position: 'Position',
    rotate: 'Angle',
    name: 'Name',
    icon: 'Icon',
    trigger_position: 'Trigger Position',
    asc: 'Ascending Order',
    desc: 'Descending Order',
    sort: 'Sort',
    filter: 'Filter',
    none: 'None',
    background: 'Background',

    border: 'Corner',
    border_width: 'Border width',
    border_radius: 'Border radius',
    alpha: 'Transparency',
    add_filter: 'Add Filter',
    no_limit: 'No limit',
    filter_eq: 'Equal',
    filter_not_eq: 'Not Equal',
    filter_lt: 'Less Than',
    filter_le: 'Less than or equal to',
    filter_gt: 'Greater than',
    filter_ge: 'Greater than or equal to',
    filter_null: 'Null',
    filter_not_null: 'Not Null',
    filter_empty: 'Empty String',
    filter_not_empty: 'Not Empty String',
    filter_include: 'Contain',
    filter_not_include: 'Not Contain',
    rose_type: 'Rose pattern',
    radius_mode: 'Radius',
    area_mode: 'Area',
    rose_radius: 'Fillet',
    view_name: 'Chart Title',
    belong_group: 'Belong Group',
    select_group: 'Select Group',
    name_can_not_empty: 'Name cannot be empty',
    template_can_not_empty: 'Please check a Template',
    custom_count: 'Number of records',
    table_title_fontsize: 'Font size of header',
    table_item_fontsize: 'Table font size',
    table_header_bg: 'Header Background',
    table_item_bg: 'Table Background',
    table_header_font_color: 'Header Font',
    table_item_font_color: 'Table Font',
    table_show_index: 'Show Index',
    stripe: 'Zebra pattern',
    start_angle: 'Start Angle',
    end_angle: 'End Angle',
    style_priority: 'Style Priority',
    dashboard: 'Dashboard',
    dimension_color: 'Name Color',
    quota_color: 'Value Color',
    dimension_font_size: 'Name FontSize',
    quota_font_size: 'Value FontSize',
    space_split: 'Name/Value Space',
    only_one_quota: 'Only support 1 quota',
    only_one_result: 'Only show first result',
    dimension_show: 'Name Show',
    quota_show: 'Value Show',
    title_limit: 'Title cannot be greater than 50 characters',
    filter_condition: 'Filter Condition',
    filter_field_can_null: 'Filter field must choose',
    preview_100_data: 'Preview 100 rows',
    chart_table_normal: 'Summary Table',
    chart_table_info: 'Detail Table',
    chart_card: 'KPI Card',
    chart_bar: 'Base Bar',
    chart_bar_stack: 'Stack Bar',
    chart_percentage_bar_stack: 'Percentage Stack Bar',
    chart_bar_horizontal: 'Horizontal Bar',
    chart_bar_stack_horizontal: 'Stack Horizontal Bar',
    chart_percentage_bar_stack_horizontal: 'Horizontal Percentage Stack Bar',
    chart_line: 'Base Line',
    chart_line_stack: 'Stack Line',
    chart_pie: 'Pie',
    chart_pie_donut: 'Donut Pie',
    chart_pie_rose: 'Rose Pie',
    chart_pie_donut_rose: 'Rose Donut Pie',
    chart_funnel: 'Funnel',
    chart_radar: 'Radar',
    chart_gauge: 'Gauge',
    chart_map: 'Map',
    dateStyle: 'Date Style',
    datePattern: 'Date Format',
    y: 'Year',
    y_M: 'Year Month',
    y_Q: 'Year Quarter',
    y_W: 'Year Week',
    y_M_d: 'Year Month Day',
    H_m_s: 'Hour Minute Second',
    y_M_d_H_m: 'Year Month Day Hour Minute',
    y_M_d_H_m_s: 'Year Month Day Hour Minute Second',
    date_sub: 'yyyy-MM-dd',
    date_split: 'yyyy/MM/dd',
    chartName: 'New Chart',
    chart_show_error: 'can not show normal',
    chart_error_tips: 'Please contact admin ',
    title_cannot_empty: 'Title can not be empty',
    table_title_height: 'Table header height',
    table_item_height: 'Table row height',
    axis_show: 'Axis Show',
    axis_color: 'Axis Color',
    axis_width: 'Axis Width',
    axis_type: 'Axis Type',
    grid_show: 'Grid Show',
    grid_color: 'Grid Color',
    grid_width: 'Grid Width',
    grid_type: 'Grid Type',
    axis_type_solid: 'Solid',
    axis_type_dashed: 'Dashed',
    axis_type_dotted: 'Dotted',
    axis_label_show: 'Label Show',
    axis_label_color: 'Label Color',
    axis_label_fontsize: 'Label Fontsize',
    text_style: 'Font Style',
    bolder: 'Bolder',
    change_ds: 'Change Dataset',
    change_ds_tip: 'Tips：Change Dataset will change fields,you need rebuild chart',
    axis_name_color: 'Name Color',
    axis_name_fontsize: 'Name Fontsize',
    pie_label_line_show: 'Line',
    outside: 'Outside',
    center: 'Center',
    split: 'Axis',
    axis_line: 'Axis Line',
    axis_label: 'Axis Label',
    label_fontsize: 'Label Fontsize',
    split_line: 'Split Line',
    split_color: 'Split Color',
    shadow: 'Shadow',
    condition: 'Filter Value',
    filter_value_can_null: 'Filter value can not empty',
    filter_like: 'Contain',
    filter_not_like: 'Not Contain',
    filter_in: 'IN',
    filter_not_in: 'NOT IN',
    chart_details: 'Chart Details',
    details: 'Details',
    image: 'Image',
    export_details: 'Export Details',
    export: 'Export',
    color_light: 'Light',
    color_classical: 'Classical',
    color_fresh: 'Fresh',
    color_energy: 'Energy',
    color_red: 'Red',
    color_fast: 'Fast',
    color_spiritual: 'Spiritual',
    chart_data: 'Data',
    chart_style: 'Style',
    drag_block_type_axis: 'Type Axis',
    drag_block_value_axis: 'Value Axis',
    drag_block_table_data_column: 'Data Column',
    drag_block_pie_angel: 'Sector Angle',
    drag_block_pie_label: 'Sector Label',
    drag_block_gauge_angel: 'Pointer Angle',
    drag_block_label_value: 'Value',
    drag_block_funnel_width: 'Funnel Width',
    drag_block_funnel_split: 'Funnel Split',
    drag_block_radar_length: 'Branch Length',
    drag_block_radar_label: 'Branch Label',
    stack_item: 'Stack Item',
    map_range: 'Map range',
    select_map_range: 'Please select map range',
    area: 'Area',
    placeholder_field: 'Drag Field To Here',
    axis_label_rotate: 'Label Rotate',
    chart_scatter_bubble: 'Bubble',
    chart_scatter: 'Scatter',
    bubble_size: 'Bubble Size',
    chart_treemap: 'Tree Map',
    drill: 'Drill',
    drag_block_treemap_label: 'Color Label',
    drag_block_treemap_size: 'Color Size',
    bubble_symbol: 'Shape',
    gap_width: 'Gap Width',
    width: 'Width',
    height: 'Height',
    system_case: 'System',
    custom_case: 'Custom',
    last_layer: 'This Is The Last Layer',
    radar_size: 'Size',
    chart_mix: 'Mix',
    axis_value: 'Axis Value',
    axis_value_min: 'Min',
    axis_value_max: 'Max',
    axis_value_split: 'Split',
    axis_auto: 'Auto',
    table_info_switch: 'Switch detail table will clear dimensions',
    drag_block_value_axis_main: 'Main Axis Value',
    drag_block_value_axis_ext: 'Ext Axis Value',
    yAxis_main: 'Main Vertical Axis',
    yAxis_ext: 'Ext Vertical Axis',
    total: 'Total',
    items: 'Items',
    chart_liquid: 'Liquid',
    drag_block_progress: 'Progress',
    liquid_max: 'End Value',
    liquid_outline_border: 'Border Width',
    liquid_outline_distance: 'Border Distance',
    liquid_wave_length: 'Wave Length',
    liquid_wave_count: 'Wave Count',
    liquid_shape: 'Shape',
    liquid_shape_circle: 'Circle',
    liquid_shape_diamond: 'Diamond',
    liquid_shape_triangle: 'Triangle',
    liquid_shape_pin: 'Pin',
    liquid_shape_rect: 'Rect',
    dimension_or_quota: 'Dimension Or Quota',
    axis_value_split_count: 'Tick Count',
    axis_value_split_space: 'Tick Interval',
    chart_waterfall: 'Waterfall',
    pie_inner_radius_percent: 'Inner Radius(%)',
    pie_outer_radius_size: 'Outer Radius',
    table_page_size: 'Page Size',
    table_page_size_unit: 'Item/Page',
    result_count: 'Result',
    result_mode_all: 'ALL',
    result_mode_custom: 'Custom',
    chart_word_cloud: 'Word Cloud',
    drag_block_word_cloud_label: 'Word Label',
    drag_block_word_cloud_size: 'Word Size',
    splitCount_less_100: 'Split Count Range 0-100',
    change_chart_type: 'Change Type',
    chart_type_table: 'Table',
    chart_type_quota: 'Quota',
    chart_type_trend: 'Trend',
    chart_type_compare: 'Compare',
    chart_type_distribute: 'Distribute',
    chart_type_relation: 'Relation',
    chart_type_space: 'Space',
    preview: 'Preview',
    next: 'Next',
    select_dataset: 'Select Dataset',
    select_chart_type: 'Select Chart Type',
    recover: 'Reset',
    yoy_label: 'YOY/MOM',
    yoy_setting: 'Setting',
    pls_select_field: 'Select Field',
    compare_date: 'Compare Date',
    compare_type: 'Compare Type',
    compare_data: 'Data Setting',
    year_yoy: 'Year yoy',
    month_yoy: 'Month yoy',
    quarter_yoy: 'Quarter yoy',
    week_yoy: 'Week yoy',
    day_yoy: 'Day yoy',
    year_mom: 'Year mom',
    month_mom: 'Month mom',
    quarter_mom: 'Quarter mom',
    week_mom: 'Week mom',
    day_mom: 'Day mom',
    data_sub: 'Sub',
    data_percent: 'Percent',
    compare_calc_expression: 'Expression',
    and: 'And',
    or: 'Or',
    logic_exp: 'Logic',
    enum_exp: 'Enum',
    pls_slc: 'Please Select',
    filter_exp: 'Filter Value',
    filter_type: 'Filter Type',
    filter_value_can_not_str: 'Value type can not input string',
    enum_value_can_not_null: 'Enum Value can not empty.',
    table_config: 'Table Config',
    table_column_width_config: 'Column Width',
    table_column_adapt: 'Adapt',
    table_column_custom: 'Custom',
    chart_table_pivot: 'Pivot Table',
    table_pivot_row: 'Data Row',
    field_error_tips: 'This field is changed(Include dimension、quota，field type，deleted),please edit again.',
    mark_field_error: 'The current field does not exist, please select again',
    table_border_color: 'Border Color',
    table_header_align: 'Header Align',
    table_item_align: 'Body Align',
    table_align_left: 'Left',
    table_align_center: 'Center',
    table_align_right: 'Right',
    table_scroll_bar_color: 'Scroll Bar Color',
    draw_back: 'Draw Back',
    senior: 'Senior',
    senior_cfg: 'Senior Config',
    function_cfg: 'Function Config',
    analyse_cfg: 'Analyse',
    slider: 'Slider',
    slider_range: 'Range',
    slider_bg: 'Background',
    slider_fill_bg: 'Selected Background',
    slider_text_color: 'Font Color',
    chart_no_senior: 'This chart type not support senior config,please look forward to.',
    chart_no_properties: 'This chart type not support properties config.',
    assist_line: 'Assist Line',
    field_fixed: 'Fixed',
    line_type_dotted: 'Dotted',
    value_can_not_empty: 'Value can not be empty',
    value_error: 'Value illegal',
    threshold: 'Threshold',
    threshold_range: 'Range',
    gauge_threshold_format_error: 'Format Error',
    total_cfg: 'Total Config',
    col_cfg: 'Column',
    row_cfg: 'Row',
    total_show: 'Total',
    total_position: 'Position',
    total_label: 'Alias',
    sub_total_show: 'Sub Total',
    total_pos_top: 'Top',
    total_pos_bottom: 'Bottom',
    total_pos_left: 'Left',
    total_pos_right: 'Right',
    chart_label: 'Label',
    drag_block_label: 'Label',
    count_distinct: 'Distinct Count',
    table_page_mode: 'Page Mode',
    page_mode_page: 'Page',
    page_mode_pull: 'Pull',
    exp_can_not_empty: 'Condition can not be empty',
    value_formatter: 'Value Formatter',
    value_formatter_type: 'Formatter Type',
    value_formatter_auto: 'Auto',
    value_formatter_value: 'Value',
    value_formatter_percent: 'Percent',
    value_formatter_unit: 'Unit',
    value_formatter_decimal_count: 'Decimal Count',
    value_formatter_suffix: 'Unit Suffix',
    value_formatter_thousand_separator: 'Thousand Separator',
    value_formatter_example: 'Example',
    unit_none: 'None',
    unit_thousand: 'Thousand',
    unit_ten_thousand: 'Ten Thousand',
    unit_million: 'Million',
    unit_hundred_million: 'Hundred Million',
    formatter_decimal_count_error: 'Range 0-10',
    gauge_threshold_compare_error: 'Range must added',
    tick_count: 'Tick Split',
    custom_sort: 'Custom',
    custom_sort_tip: 'Custom sort field first,and only support single field',
    clean_custom_sort: 'Clean',
    ds_field_edit: 'Dataset Field Manage',
    chart_field_edit: 'Chart Field Manage',
    copy_field: 'Copy Field',
    calc_field: 'Calculate Field',
    form_type: 'From Type',
    scroll_cfg: 'Scroll Config',
    scroll: 'Scroll',
    open: 'Open',
    row: 'Row',
    interval: 'Interval',
    max_more_than_mix: 'Max must more than Min',
    field: 'Field',
    textColor: 'Text Color',
    backgroundColor: 'Background Color',
    field_can_not_empty: 'Field can not empty',
    conditions_can_not_empty: 'Conditions can not be empty，if unnecessary，please delete the field',
    remark: 'Remark',
    remark_edit: 'Edit Remark',
    remark_bg_color: 'Background Fill',
    quota_font_family: 'Value Font Family',
    quota_text_style: 'Value Style',
    quota_letter_space: 'Value Letter Space',
    dimension_font_family: 'Name Font Family',
    dimension_text_style: 'Name Style',
    dimension_letter_space: 'Name Letter Space',
    font_family: 'Font Family',
    letter_space: 'Letter Space',
    font_shadow: 'Font Shadow',
    chart_area: 'Area',
    fix: 'Fix',
    dynamic: 'Dynamic',
    gauge_size_field_delete: 'Dynamic field changed，please edit again',
    chart_group: 'Sub Type',
    chart_bar_group: 'Bar Group',
    chart_bar_group_stack: 'Group Stack Bar',
    field_dynamic: 'Dynamic',
    aggregation: 'Aggregation',
    filter_between: 'Between',
    field_not_empty: 'Field can not be empty',
    summary_not_empty: 'Summary can not be empty',
    reserve_zero: '0',
    reserve_one: '1',
    reserve_two: '2',
    proportion: 'Proportion',
    label_content: 'Label Content',
    percent: 'Percent',
    table_index_desc: 'Index Header Name',
    total_sort: 'Total Sort',
    total_sort_none: 'None',
    total_sort_asc: 'ASC',
    total_sort_desc: 'DESC',
    total_sort_field: 'Sort Field',
    empty_data_strategy: 'Empty Data Strategy',
    break_line: 'Keep',
    set_zero: 'Set Zero',
    ignore_data: 'Hide Data',
    sub_dimension_tip: 'This field is required, and cannot be included in the type axis, you should choose non-group chart if you don\'t need it, or you will get unexpected chart.',
    drill_dimension_tip: 'Only fields in the dataset can be drilled',
    table_scroll_tip: 'The detail table is only effective when the pagination mode is "Drop-down".',
    table_threshold_tip: 'Tip: Do not select fields repeatedly. If the same field is configured repeatedly, only the last field will take effect.',
    table_column_width_tip: `Column width do not always work.<br/>
                             The priority of the container width is higher than the column width, <br/>
                             which means if the result of dividing the width of the table container by the number of columns is greater than specified column width, <br/>
                             the former will take effect.`,
    reference_field_tip: `Reference fields start with "[" and end with "]". <br/>
                          Do not modify the reference content, otherwise the reference will fail.<br/>
                          If you enter content in the same format as the reference field, it will be treated as a reference field.`,
    scatter_tip: 'When this indicator is in effect, the bubble size attribute in the style size will be invalid',
    place_name_mapping: 'Place name mapping',
    axis_tip: 'The minimum value, maximum value, and interval are all numeric types; it will be regarded as automatic if left blank.<br/>Please make sure that the filled values can be calculated correctly, otherwise the axis values will not be displayed normally.',
    format_tip: `The template variables include {a}, {b}, {c}, {d}, which represent series name, data name, data value, etc. respectively.<br>
                    When the trigger position is 'coordinate axis', there will be multiple series of data. At this time, the index of the series can be represented by {a0}, {a1}, {a2} followed by an index.<br>
                    {a}, {b}, {c}, {d} have different meanings under different graph types. Among them, variables {a}, {b}, {c}, {d} represent data meanings in different chart types:<br><br>
                    Line (area) chart, Column (Bar) chart, Dashboard: {a} is series name, {b} is category value, {c} is value<br>
                    Pie chart, Funnel chart: {a} is series name, {b} is data item name, {c} is value, {d} is percentage<br>
                    Map : {a} (series name), {b} is area name, {c} is merged values, {d} is none<br>
                    Scatter (Bubble) plot: {a} is series name, {b} is data name, {c} is numeric array, {d} is none`
  },
  dataset: {
    spend_time: 'Spend',
    sql: 'SQL',
    sql_result: 'Result',
    parse_filed: 'Parse Field',
    field_rename: 'Rename Field',
    params_work: 'Effective only when editing SQL',
    sql_variable_limit_1: '1、SQL variables can only be used in where conditions',
    sql_variable_limit_2: '2、Example：select * from table_name where column_name1=\'${param_name1}\' and column_name2 in \'${param_name2}\'',
    select_year: 'Select Year',
    select_month: 'Select Month',
    select_date: 'Select Date',
    select_time: 'Select Time',
    time_year: 'Date-Year',
    time_year_month: 'Date-Yearmonth',
    time_year_month_day: 'Date-YearMonthDay',
    time_all: 'TIme',
    dataset_sync: ' ( Data sync... )',
    sheet_warn: 'There are multiple sheet pages, and the first one is extracted by default',
    datalist: 'Data Set',
    name: 'DataSet Name',
    add_group: 'Add Group',
    add_scene: 'Add Scene',
    group: 'Group',
    scene: 'Scene',
    delete: 'Delete',
    move_to: 'Move',
    rename: 'Rename',
    tips: 'Tips',
    confirm_delete: 'confirm_delete',
    confirm_delete_msg: 'Deleting a dataset will affect its related user-defined dataset, associated dataset and dashboard. Confirm the delete？',
    delete_success: 'delete_success',
    confirm: 'confirm',
    cancel: 'cancel',
    search: 'search',
    back: 'back',
    add_table: 'Add Table',
    process: 'Speed of progress',
    update: 'update',
    db_data: 'Database Dataset',
    sql_data: 'SQL Dataset',
    excel_data: 'Excel Dataset',
    custom_data: 'Custom Dataset',
    pls_slc_tbl_left: 'Please select the chart from the left',
    add_db_table: 'Add Database Dataset',
    add_api_table: 'Add API Dataset',
    pls_slc_data_source: 'Please select data source',
    table: 'Table',
    edit: 'Edit',
    create_view: 'Create Chart',
    data_preview: 'Data preview',
    field_type: 'Field Type',
    field_name: 'Field Name',
    field_origin_name: 'Field Origin Name',
    field_check: 'Selected',
    update_info: 'Update Info',
    update_records: 'Update Records',
    join_view: 'Data Associated',
    text: 'Text',
    time: 'Time',
    value: 'Value',
    mode: 'Mode',
    direct_connect: 'Direct Link',
    sync_data: 'Timing synchronization',
    update_setting: 'Update Setting',
    sync_now: 'Update Now',
    add_task: 'Add Task',
    task_name: 'Task Name',
    task_id: 'Task ID',
    start_time: 'Start Time',
    end_time: 'End Time',
    status: 'State',
    error: 'Error',
    completed: 'Completed',
    underway: 'underway',
    task_update: 'Update Setting',
    update_type: 'Update Type',
    all_scope: 'Full update',
    add_scope: 'Incremental update',
    select_data_time: 'Select date time',
    execute_rate: 'Execution frequency',
    execute_once: 'Execution Now',
    simple_cron: 'Simple repeat',
    cron_config: 'Expression setting',
    no_limit: 'No limit',
    set_end_time: 'Set the end time',
    operate: 'operation',
    save_success: 'Saved Successfully',
    close: 'Close',
    required: 'Required',
    input_content: 'Please input the content',
    add_sql_table: 'Add SQL Dataset',
    preview: 'Preview',
    pls_input_name: 'Please enter a name',
    connect_mode: 'Connection Mode',
    incremental_update_type: 'Incremental update mode',
    incremental_add: 'Incremental Addition',
    incremental_delete: 'Incremental Deletion',
    last_update_time: 'Last update time',
    current_update_time: 'Current update time',
    param: 'Parameter',
    edit_sql: 'Edit SQL Dataset',
    showRow: 'Display line',
    add_excel_table: 'Add excel dataset',
    add_custom_table: 'Add self help dataset',
    upload_file: 'Upload File',
    detail: 'Details',
    type: 'Type',
    create_by: 'Creator',
    create_time: 'Create_time',
    preview_show: 'Display',
    preview_item: 'items data',
    preview_total: 'Total',
    pls_input_less_5: 'Please input integer less 5',
    field_edit: 'Edit Field',
    table_already_add_to: 'This table is already add to',
    uploading: 'Uploading...',
    add_union: 'Create Associations',
    union_setting: 'Association Settings',
    pls_slc_union_field: 'Please select associated field',
    pls_slc_union_table: 'Please select association table',
    source_table: 'Association table',
    source_field: 'Associated fields',
    target_table: 'Associated table',
    target_field: 'Associated field',
    union_relation: 'Relationship',
    pls_setting_union_success: 'Please set the relationship correctly',
    invalid_dataset: 'Kettle is not running, invalid dataset',
    check_all: 'Select all',
    can_not_union_self: 'The associated table cannot be the same as the associated table',
    float: 'Decimal',
    edit_custom_table: 'Edit self help dataset',
    edit_field: 'Edit Field',
    preview_100_data: 'Show 100 lines data',
    invalid_table_check: 'Please sync data first.',
    parse_error: 'Parse failed,please check.Reference：https://dataease.io/docs/user_manual/dataset_configuration/dataset_Excel',
    origin_field_type: 'Field Origin Type',
    edit_excel_table: 'Edit Excel Dataset',
    edit_excel: 'Edit Excel',
    excel_replace: 'Replace',
    excel_add: 'Add',
    dataset_group: 'Dataset Group',
    m1: 'Move ',
    m2: ' To',
    char_can_not_more_50: 'Dataset name can not more 50',
    task_add_title: 'Add Task',
    task_edit_title: 'Edit Task',
    sync_latter: 'Sync latter',
    task: {
      list: 'Task list',
      record: 'Execution record',
      create: 'New task',
      name: 'Task name',
      last_exec_time: 'Last execution time',
      next_exec_time: 'Next execution time',
      last_exec_status: 'Last execution result',
      task_status: 'Task status',
      dataset: 'Data set',
      search_by_name: 'Search by name',
      underway: 'Waiting for execution',
      stopped: 'End',
      exec: 'underway',
      pending: 'Pause',
      confirm_exec: 'Manual trigger execution？',
      change_success: 'State switch successful',
      excel_replace_msg: 'Calculation fields, custom datasets, associated datasets, dashboards, etc. may be affected. Confirm the replacement？',
      effect_ext_field: 'Affect calculated fields'
    },
    field_group_type: 'Type',
    location: 'Location',
    left_join: 'LEFT JOIN',
    right_join: 'RIGHT JOIN',
    inner_join: 'INNER JOIN',
    full_join: 'FULL JOIN',
    can_not_union_diff_datasource: 'Union dataset must have same data source',
    operator: 'Operator',
    d_q_trans: 'Dimension/Quota Transform',
    add_calc_field: 'Create calc field',
    input_name: 'Please input name',
    field_exp: 'Field Expression',
    data_type: 'Data Type',
    click_ref_field: 'Click Quote Field',
    click_ref_function: 'Click Quote Function',
    field_manage: 'Field Manage',
    edit_calc_field: 'Edit calc field',
    calc_field: 'Calc Field',
    show_sql: 'Show SQL',
    ple_select_excel: 'Please select excel file to import',
    merge: 'Merge',
    no_merge: 'Dont Merge',
    merge_msg: 'If the fields in the data table are consistent, merge them into one data set?',
    merge_title: 'Merge data',
    field_name_less_50: 'Field name can not more 50 chars.',
    excel_info_1: '1、Merged cells cannot exist in the Excel file；',
    excel_info_2: '2、The first line of the Excel file is the title line, which cannot be empty or date；',
    excel_info_3: '3、The file size shall not exceed 500m。',
    sync_field: 'Sync Field',
    confirm_sync_field: 'Confirm Sync',
    confirm_sync_field_tips: 'Sync field maybe change edit field，please confirm',
    sync_success: 'Success',
    sync_success_1: 'Success，please sync data again',
    row_permission: {
      type: 'Type',
      name: 'Name',
      condition: 'Conditions',
      value: 'Value',
      add: 'Add row permissions',
      edit: 'Edit row permissions',
      please_select_field: 'Please select a field',
      please_select_auth_type: 'Please select the authorization type',
      please_select_auth_id: 'Please select authorization target',
      row_permission_not_empty: 'Row permission cannot be empty',
      search_by_filed_name: 'Search by field name',
      auth_type: 'Authorization type',
      auth_obj: 'Authorized object'
    },
    column_permission: {
      add: 'Add',
      edit: 'Edit',
      please_select_field: 'Please select field',
      please_select_auth_type: 'Please select the authorization type',
      please_select_auth_id: 'Please select authorization obj',
      column_permission_not_empty: 'Please select authorization target',
      auth_type: 'Authorization type',
      auth_obj: 'Authorized object',
      enable: 'Enable',
      disable: 'Disable',
      prohibit: 'Prohibit',
      desensitization: 'Desensitization',
      desensitization_rule: 'Desensitization rule',
      m: 'M ',
      n: 'N ',
      mgtn: 'M Cannot be greater than N'
    },
    row_permissions: 'Row Permissions',
    column_permissions: 'Column Permissions',
    row_column_permissions: 'Row And Column Permissions',
    union_data: 'Union Dataset',
    add_union_table: 'Add Union Dataset',
    edit_union: 'Edit Union Dataset',
    union: 'Union',
    edit_union_relation: 'Edit Union Relation',
    add_union_relation: 'Add Union Relation',
    field_select: 'Select Field',
    add_union_field: 'Add Union Field',
    union_error: 'Union relation and field can not be empty',
    union_repeat: 'This dataset is already union，do not union repeat',
    preview_result: 'Preview',
    sql_ds_union_error: 'Direct connect SQL dataset can not be union',
    api_data: 'API dataset',
    copy: 'Copy',
    sync_log: 'Sync log',
    field_edit_name: 'Field Name',
    input_edit_name: 'Input field name',
    edit_search: 'Search by name',
    na: 'None',
    date_format: 'Time format, default: year-month-day hour:minute:second',
    export_dataset: 'Export',
    filename: 'Filename',
    export_filter: 'Filter',
    pls_input_filename: 'Please input filename',
    calc_tips: {
      tip1: 'The expression syntax should follow the database syntax corresponding to the data source.',
      tip2: 'Aggregation operation is not supported in the dataset.',
      tip3: 'The reference field starts with "[" and ends with "]"',
      tip4: 'Do not modify the reference content, otherwise the reference will fail',
      tip5: 'If you enter content in the same format as the reference field, it will be treated as a reference field',
      tip6: 'Use the functions supported by the database type corresponding to the dataset. The syntax is the same as that of the corresponding database',
      tip7: 'For example, date format: MySQL uses DATE_ FORMAT(date,format)； Oracle uses TO_ DATE(X,[,fmt])',
      tip8: 'Non direct connection mode data set, use Doris database functions, refer to Doris official website'
    }
  },
  driver: {
    driver: 'Driver',
    please_choose_driver: 'Please choose driver',
    mgm: 'Driver',
    exit_mgm: 'Exit Driver',
    add: 'ADD Driver',
    modify: 'Modify',
    show_info: 'Driver info',
    file_name: 'File name',
    version: 'version',
    please_set_driverClass: 'Please specify driver class'
  },
  datasource: {
    data_source_configuration: 'Data Source Configuration',
    data_source_table: 'Data Source Table',
    auth_method: 'Auth method',
    passwd: 'UserName Password',
    kerbers_info: 'Please make sure krb5 Conf, KeyTab key, added to path: /opt/dataease/conf',
    client_principal: 'Client Principal',
    keytab_Key_path: 'Keytab Key Path',
    datasource: 'Data Source',
    please_select_left: 'Please select data from the left',
    show_info: 'Data Source Info',
    create: 'Create Data Source',
    type: 'Type',
    please_choose_type: 'Please select data source type',
    please_choose_data_type: 'Please select the calculation mode',
    data_base: 'Database name',
    user_name: 'User Name',
    password: 'Password',
    host: 'Host name / IP address',
    doris_host: 'Doris Addr',
    query_port: 'Query Port',
    http_port: 'Http Port',
    port: 'Port',
    datasource_url: 'URL address',
    please_input_datasource_url: 'Please enter Elasticsearch URL address，e.g: http://es_host:es_port',
    please_input_data_base: 'Please enter the database name',
    please_select_oracle_type: 'Select connection type',
    please_input_user_name: 'Please enter user name',
    please_input_password: 'Please enter Password',
    please_input_host: 'Please enter host',
    please_input_url: 'Please enter url address',
    please_input_port: 'Please enter port',
    modify: 'Edit data Source',
    copy: 'Copy datasource',
    validate_success: 'Verification successful',
    validate: 'Validate',
    search_by_name: 'Search by name',
    delete_warning: 'Confirm to delete?',
    input_name: 'Please input name',
    input_limit_2_25: '2-25 chars',
    input_limit_2_50: '2-50 chars',
    input_limit: '{num} chars',
    oracle_connection_type: 'Service Name/SID',
    oracle_sid: 'SID',
    oracle_service_name: 'Service Name',
    get_schema: 'Get Schema',
    schema: 'Database Schema',
    please_choose_schema: 'Please select Schema',
    charset: 'Charset',
    targetCharset: 'Target Charset',
    please_choose_targetCharset: 'Please select target charset',
    please_choose_charset: 'Please select charset',
    edit_datasource_msg: 'Modifying the data source information may make the data set under the modified data source unavailable. Confirm the modification？',
    repeat_datasource_msg: 'Data source information with the same configuration already exists, ',
    confirm_save: 'Confirm save?',
    in_valid: 'Invalid datasource',
    initial_pool_size: 'Initial connections',
    min_pool_size: 'Minimum of connections',
    max_pool_size: 'Maximum connection',
    max_idle_time: 'Maximum idle (seconds)',
    bucket_num: 'Bucket number',
    replication_num: 'Replication number',
    please_input_bucket_num: 'Please enter  Bucket number',
    please_input_replication_num: 'Please enter Replication number',
    acquire_increment: 'Growth number',
    connect_timeout: 'Connection timeout (seconds)',
    please_input_initial_pool_size: 'Please enter the number of initial connections',
    please_input_min_pool_size: 'Please enter the minimum number of connections',
    please_input_max_pool_size: 'Please enter the maximum number of connections',
    please_input_max_idle_time: 'Please enter the maximum idle (seconds)',
    please_input_acquire_increment: 'Please enter the growth number',
    please_input_query_timeout: 'Please enter query timeout',
    please_input_connect_timeout: 'Please enter the connection timeout (seconds)',
    no_less_then_0: 'Parameters in advanced settings cannot be less than zero',
    port_no_less_then_0: 'Port cannot be less than zero',
    priority: 'Advanced setting',
    data_mode: 'Data mode',
    direct: 'Direct Mode',
    extract: 'Extraction mode',
    all_compute_mode: 'Direct connection and extraction mode',
    extra_params: 'Extra JDBC connection string',
    please_input_dataPath: 'Please enter the JsonPath data path',
    warning: 'Contains invalid table',
    data_table: 'Dataset Table',
    data_table_name: 'Dataset Table name',
    method: 'Request mode',
    url: 'URL',
    add_api_table: 'Add API table',
    edit_api_table: 'Edit API table',
    base_info: 'Basic information',
    column_info: 'Data structure',
    request: 'Request',
    path_all_info: 'Please fill in the full address',
    req_param: 'Request parameters',
    headers: 'Request header',
    key: 'Key',
    value: 'Value',
    data_path: 'Extract data',
    data_path_desc: 'Please fill in the data path with Jsonpath',
    body_form_data: 'form-data',
    body_x_www_from_urlencoded: 'x-www-form-urlencoded',
    body_json: 'json',
    body_xml: 'xml',
    body_raw: 'row',
    request_body: 'Request Body',
    auth_config: 'Auth config',
    auth_config_info: 'Permission verification is required for the request',
    verified: 'Verified',
    verification_method: 'Verification Method',
    username: 'Username',
    api_table_not_empty: 'API data table cannot be empty',
    has_repeat_name: 'Duplicate API data table name',
    has_repeat_field_name: 'The field name is duplicate, please modify it before selecting',
    api_field_not_empty: 'Field cannot be empty',
    success_copy: 'Copy succeeded',
    valid: 'Valid',
    invalid: 'Invalid',
    api_step_1: 'Connection API',
    api_step_2: 'Extract data',
    _ip_address: 'Please enter host name / IP address',
    display_name: 'Display name:',
    connection_mode: 'Connection mode:',
    driver_file: 'Driver file',
    edit_driver: 'Edit driver',
    driver_name: 'Driver name:',
    drive_type: 'Drive type',
    add_driver: 'Add driver',
    diver_on_the_left: 'Please select the driver on the left',
    no_data_table: 'No data table',
    on_the_left: 'Please select the data source on the left',
    table_name: 'Table name:',
    create_dataset: 'Create dataset',
    field_description: 'Field description',
    table_description: 'Table description',
    relational_database: 'Relational database',
    data_warehouse_lake: 'Data Warehouse/Data Lake',
    non_relational_database: 'Non relational database',
    all: 'All',
    other: 'other',
    this_data_source: 'Are you sure to delete this data source?',
    delete_this_dataset: 'Are you sure to delete this dataset?',
    cannot_be_deleted_dataset: 'This dataset has the following blood relationship. Deleting it will cause the view of related dashboard to be invalid. Are you sure to delete it?',
    cannot_be_deleted_datasource: 'This datasource has the following blood relationship. Deleting it will cause the view of related dashboard to be invalid. Are you sure to delete it?',
    edit_folder: 'Edit Folder',
    click_to_check: 'Click to check the blood relationship',
    delete_this_item: 'Do you want to delete this item?',
    can_be_uploaded: 'Only files in jar format can be uploaded',
    query_timeout: 'query timeout',
    add_data_source: 'add data source',
    delete_this_driver: 'Are you sure to delete this driver?',
    basic_info: 'Basic Info'
  },
  pblink: {
    key_pwd: 'Please enter the password to open the link',
    input_placeholder: 'Please enter the 4-digits-letters',
    pwd_error: 'Wrong password',
    pwd_format_error: 'Please enter the 4-digits-letters',
    sure_bt: 'Confirm',
    back_parent: 'Back to previous'
  },
  panel: {
    url_check_error: 'Jump error, Illegal URL',
    view_style: 'View Style',
    view_color_setting: 'View Color Setting',
    border_color_setting: 'Border Color',
    unpublished_tips: 'After unpublishing, the panel cannot be viewed. Are you sure you want to cancel publishing? ',
    position_adjust_component: 'Position adjust',
    active_font_size: 'Selected font size',
    carousel: 'Carousel',
    switch_time: 'Switch time',
    position_adjust: 'Position',
    space_top: 'Top',
    space_left: 'Left',
    space_width: 'Width',
    space_height: 'Height',
    to_top: 'To Top',
    down: 'Down',
    mobile_style_setting: 'Style setting',
    mobile_style_setting_tips: 'Customize the mobile background',
    board: 'Border',
    text: 'Text',
    board_background: 'Background',
    title_color: 'Title color',
    input_style: 'Input box style (color)',
    overall_setting: 'Overall setting',
    panel_background: 'Panel background',
    component_color: 'Component color',
    chart_title: 'Chart title',
    filter_component: 'Filter component',
    enable_refresh_view: 'Enable refresh',
    enable_view_loading: 'View loading prompt',
    image_size_tips: 'Please do not exceed 15M in the picture',
    image_add_tips: 'Only pictures can be inserted',
    watermark: 'Watermark',
    panel_get_data_error: 'Failed to obtain panel information. The panel may have been deleted. Please check the panel status',
    panel_no_save_tips: 'There are unsaved panel',
    panel_cache_use_tips: 'It was checked that the last dashboard could not be saved normally. Do you want to use the panel that was not saved last time?',
    template_name_tips: 'Panel\'s name should not be null',
    panel_background_item: 'Customize panel background',
    panel_background_image_tips: 'Currently.Jpeg,.Jpg,.Png,.Gif files are supported, and the size should not exceed 15m',
    reUpload: 'reUpload',
    create_by: 'Create By',
    create_time: 'Create Time',
    update_by: 'Update By',
    update_time: 'Update Time',
    target_url: 'Target Url',
    target_url_tips: 'You can click fields to splice URLs or parameters',
    select_world: 'Select World',
    template_market: 'Template Market',
    template_preview: 'Template Preview',
    apply: 'Apply',
    apply_this_template: 'Apply This Template',
    market_network_tips: 'View template Market template requires server and template Market（ https://dataease.io/templates ）, please check the network... ',
    enter_name_tips: 'Please enter the name of the panel',
    name: 'Name',
    apply_template: 'Apply Template',
    enter_template_name_tips: 'Please enter the template name...',
    pic_adaptation: 'Adaptation',
    pic_equiratio: 'Equiratio',
    pic_original: 'Original',
    pic_size: 'Image Size',
    web_addr: 'Web Address',
    stream_media_info: 'Stream Media Info',
    video_info: 'Video Info',
    title_position: 'Title Position',
    tab_inner_style: 'Tab Inner Style',
    data_format: 'Data Format',
    border_color: 'Border Color',
    theme_change_warn: 'Subject Change',
    theme_change_tips: 'Changing the theme will overwrite the view related theme attributes. It is recommended to back up in advance. Do you want to continue the replacement?',
    theme_color_change_warn: 'Theme Color Change',
    theme_color_change_tips: 'Theme Color change will overwrite the original view properties',
    theme_color: 'Theme Color',
    theme_color_dark: 'Dark',
    theme_color_light: 'Light',
    refresh_frequency: 'Refresh Frequency',
    card_color_matching: 'Card Color Matching',
    table_color_matching: 'Table Color Matching',
    background_color: 'Background Color',
    more: 'More',
    level: 'Level',
    enlarge: 'Enlarge',
    panel_style: 'Panel Style',
    multiplexing: 'Multiplexing',
    panel_off: 'Off the shelf',
    batch_opt: 'Batch Operation',
    edit_leave_tips: 'Do You Want To Abandon And Leave The Current Page?',
    hyperlinks: 'Hyperlinks',
    is_live: 'Is Live',
    yes: 'Yes',
    no: 'No',
    live_tips: 'User Https First',
    stream_media_add_tips: 'And Add Stream Media Info...',
    stream_mobile_tips: 'IOS terminal may not display',
    json_params_error: 'Third Party Parameters Parsing Failed. Please Check Whether The Parameters Format Is Correct',
    inner_padding: 'Inner Padding',
    board_radio: 'Board Radio',
    background: 'Background',
    component_style: 'Component Style',
    web_set_tips: 'Some Websites Cannot Be Displayed Because Of Not Allow Embedded ',
    repeat_params: 'Repeat Params Exist',
    enable_outer_param_set: 'Enable Outer Param Set',
    select_param: 'Please Select Param...',
    add_param_link_field: 'Add Params\' Linked Field',
    add_param: 'Add Param',
    enable_param: 'Enable Param',
    param_name: 'Param Name',
    outer_param_set: 'Outer Param Set',
    outer_param_decode_error: 'External Parameter Parsing Error And Does Not Take Effect, Please Check',
    input_param_name: 'Please Input Param\'s Name',
    params_setting: 'Outer Params Setting',
    template_view_tips: 'Template\'s Views. Please Change',
    edit_web_tips: 'The Inner Event Can Be Used When Then Panel Not In Edit Status',
    no_auth_role: 'Unshared roles',
    auth_role: 'Shared roles',
    picture_limit: 'Only pictures can be inserted',
    drag_here: 'Please drag the left field here',
    copy_link_passwd: 'Copy link and password',
    copy_link: 'Copy link',
    copy_short_link: 'Copy short link',
    copy_short_link_passwd: 'Copy short link and password',
    passwd_protect: 'Password Protect',
    link: 'Link',
    link_share: 'Share Link',
    over_time: 'Over time',
    link_expire: 'Link is expire',
    link_share_desc: 'After opening the link, anyone can access the dashboard through this link.',
    share: 'Share',
    remove_share_confirm: 'Sure removel All share ?',
    share_in: 'Share With Me',
    share_out: 'I Share',
    who_share: 'Who share',
    when_share: 'When share',
    share_to: 'Share to',
    share_to_some: 'Share [{some}] to',
    org: 'Orgnization',
    role: 'Role',
    user: 'User',
    datalist: 'Chart List',
    group: 'Catalogue',
    panel: 'Dashboard',
    groupAdd: 'New Group',
    panelAdd: 'Create Dashboard',
    delete: 'Delete',
    move_to: 'Move',
    rename: 'Rename',
    import: 'Import',
    tips: 'Tips',
    confirm_delete: 'Confirm Delete',
    delete_success: 'Delete Success',
    confirm: 'Confirm',
    cancel: 'Cancel',
    save: 'Save',
    search: 'Search',
    back: 'Back',
    view: 'Chart',
    edit: 'Edit',
    panel_list: 'Dashboard',
    module: 'Component',
    filter_module: 'Filter Component',
    select_by_module: 'Select by Component',
    sys_template: 'System Template',
    user_template: 'User Template',
    add_category: 'Add Category',
    add_app_category: 'Add App Category',
    filter_keywords: 'Enter keywords to filter',
    dashboard_theme: 'Dashboard Theme',
    table: 'Table',
    gap: 'Gap',
    no_gap: 'No Gap',
    component_gap: 'Component Gap',
    refresh_time: 'Refresh Time',
    minute: 'minute',
    second: 'second',
    photo: 'Photo',
    default_panel: 'Default Dashboard',
    create_public_links: 'Create public links',
    to_default: 'Save To Default',
    to_default_panel: 'Save To Default Dashboard',
    store: 'Favorites',
    save_to_panel: 'Save to template',
    export_to_panel: 'Export to template',
    export_to_pdf: 'Export to PDF',
    export_to_img: 'Export to Image',
    export_to_app: 'Export to App',
    preview: 'Preview',
    fullscreen_preview: 'Fullscreen Preview',
    new_tab_preview: 'New Tab Preview',
    select_panel_from_left: 'Please select Dashboard from left',
    template_nale: 'Template name',
    template: 'Template',
    category: 'Category',
    all_org: 'All Organization',
    custom: 'Custom',
    import_template: 'Import Template',
    copy_template: 'Copy Template',
    upload_template: 'Upload Template',
    belong_to_category: 'Category',
    pls_select_belong_to_category: 'Please select category',
    template_name_cannot_be_empty: 'Template name cannot be empty',
    select_by_table: 'Select by table',
    data_list: 'Data list',
    component_list: 'Component list',
    custom_scope: 'Target',
    multiple_choice: 'Multiple choice',
    show_time: 'Show time',
    single_choice: 'Single choice',
    field: 'Field',
    unshared_people: 'Unshared people',
    shared_people: 'Shared people',
    error_data: 'Error getting data, please contact administrator',
    canvas_size: 'Canvas Size',
    canvas_scale: 'Canvas Scale',
    style: 'Style',
    clean_canvas: 'Clean Canvas',
    insert_picture: 'Insert Picture',
    redo: 'Redo',
    undo: 'Undo',
    panelNull: 'This is a Empty Dashboard，You Can Edit and Enrich It',
    copy: 'Copy',
    paste: 'Paste',
    cut: 'Cut',
    lock: 'Lock',
    topComponent: 'Top Component',
    bottomComponent: 'Bottom Component',
    upComponent: 'Up One Level',
    downComponent: 'Down One Level',
    linkage_setting: 'Linkage Setting',
    add_tab: 'Add Tab',
    open_aided_design: 'Open Component Aided Design',
    close_aided_design: 'Close Component Aided Design',
    open_style_design: 'Open Style Design',
    close_style_design: 'Close Style Design',
    matrix_design: 'Matrix Design',
    left: 'X-Axis',
    top: 'Y-Axis',
    height: 'Height',
    width: 'Width',
    color: 'Color',
    backgroundColor: 'BackgroundColor',
    borderStyle: 'Border Style',
    borderWidth: 'BorderWidth',
    borderColor: 'BorderColor',
    borderRadius: 'BorderRadius',
    fontSize: 'FontSize',
    fontWeight: 'FontWeight',
    lineHeight: 'LineHeight',
    letterSpacing: 'LetterSpacing',
    padding: 'Internal spacing',
    margin: 'Outer space',
    textAlign: 'TextAlign',
    opacity: 'Opacity',
    verticalAlign: 'Vertical Align',
    text_align_left: 'Aline Left',
    text_align_center: 'Aline Center',
    text_align_right: 'Aline Right',
    vertical_align_top: 'Align Align',
    vertical_align_middle: 'Align Middle',
    vertical_align_bottom: 'Align Bottom',
    border_style_solid: 'Solid',
    border_style_dashed: 'Dashed',
    select_component: 'Check Component',
    other_module: 'Other',
    content: 'Content',
    default_panel_name: 'Default Dashboard Name',
    source_panel_name: 'Source Dashboard Name',
    content_style: 'Content Style',
    canvas_self_adaption: 'Canvas Self Adaption',
    panel_save_tips: 'Do you want to save the changes you made to.',
    panel_save_warn_tips: 'Your changes will be lost if you don\'t save them！',
    do_not_save: 'Don\'t Save',
    save_and_close: 'Save',
    drill: 'drill',
    linkage: 'linkage',
    jump: 'Jump',
    cancel_linkage: 'Cancel Linkage',
    switch_picture: 'Switch Picture',
    select_field: 'Select View Field',
    remove_all_linkage: 'Remove All Linkage',
    exit_un_march_linkage_field: 'Exit Un March Linkage Field',
    details: 'Details',
    setting: 'Setting',
    no_drill_field: 'Miss relation field',
    matrix: 'matrix',
    suspension: 'suspension',
    new_element_distribution: 'Element Distribution',
    aided_grid: 'Aided Grid',
    aided_grid_open: 'Open',
    aided_grid_close: 'Close',
    subject_no_edit: 'System Subject Can Not Edit',
    subject_name_not_null: 'Subject Name Can Not Be Null And Less Than 20 charts',
    is_enable: 'Enable',
    open_mode: 'Open Model',
    new_window: 'New Window',
    now_window: 'Now Window',
    hyperLinks: 'target address',
    link_open_tips: 'Open When Panel Not In Edit Status',
    data_loading: 'Data Loading...',
    export_loading: 'Export Loading...',
    export_pdf: 'Export PDF',
    jump_set: 'Jump Set',
    enable_jump: 'Enable Jump',
    column_name: 'Column Name',
    enable_column: 'Enable Column',
    open_model: 'Open Model',
    link_type: 'Link Type',
    link_outer: 'Outer',
    link_panel: 'panel',
    select_jump_panel: 'Select Jump Panel',
    link_view: 'Link View',
    link_view_field: 'Link View Field',
    add_jump_field: 'Add Jump Field',
    input_jump_link: 'Input Jump Link',
    select_dimension: 'Select Dimension...',
    please_select: 'Please Select',
    video_type: 'Video Type',
    online_video: 'Online Video',
    streaming_media: 'Streaming Media',
    auto_play: 'Auto Play',
    video_tips: 'User Https,Now Format:mp4、webm',
    play_frequency: 'Play Frequency',
    play_once: 'Once',
    play_circle: 'Circle',
    video_links: 'Video Links',
    web_url: 'Web URL',
    video_add_tips: 'And Video Info...',
    link_add_tips_pre: 'Please click',
    web_add_tips_suf: 'Add Web Url Info...',
    panel_view_result_show: 'View Result',
    panel_view_result_tips: 'Chose "Panel" Will Overwrite View`s Result,Range 1~10000',
    timeout_refresh: 'Timeout，Will Refresh...',
    mobile_layout: 'Mobile Layout',
    component_hidden: 'Component Hidden',
    public_link_tips: 'Currently in public link mode, the target panel does not have a public link and cannot jump to it',
    input_title: 'Input Title',
    show_title: 'Title',
    default_settings: 'Default Settings',
    choose_background: 'Choose Component Background',
    choose_background_tips: 'The component`s own background settings will overwrite the current settings',
    setting_background: 'Set background',
    setting_jump: 'Jump settings',
    select_view: 'Please select a view...',
    visual: 'Visual',
    prohibit_multiple: 'Prohibit multiple fields in the same dataset',
    be_empty_dir: 'is empty dir',
    fold: 'Fold',
    expand: 'Expand',
    pdf_export: 'PDF Export',
    switch_pdf_template: 'Switch PDF Template'
  },
  plugin: {
    local_install: 'Local installation',
    remote_install: 'Remote installation',
    name: 'Plugin name',
    free: 'Free',
    cost: 'Cost',
    descript: 'Descript',
    version: 'Version',
    creator: 'Creator',
    install_time: 'Install Time',
    release_time: 'Time',
    un_install: 'Uninstall',
    uninstall_confirm: 'Confirm to uninstall the plugin?',
    uninstall_cancel: 'Cancel uninstall plugin',
    un_install_success: 'Uninstall is successful and restart takes effect',
    un_install_error: 'Uninstall failed, please contact the administrator'
  },
  display: {
    favicon: 'System Icon',
    logo: 'Head system logo',
    loginLogo: 'Login page header logo',
    loginImage: 'Picture on the right side of the login page',
    loginTitle: 'Login page title',
    title: 'System name',
    advice_size: 'Advice picture size',
    theme: 'Theme color',
    topMenuColor: 'Head background',
    topMenuActiveColor: 'Head selection background',
    topMenuTextColor: 'Head font color',
    topMenuTextActiveColor: 'Head font selected color',
    themeLight: 'Light',
    themeDark: 'Dark',
    themeCustom: 'Custom',
    openHomePage: 'Show Home Page',
    openMarketPage: 'Show Market Page',
    mobileBG: 'Mobile Login page BG',
    helpLink: 'Help Document Link',
    homeLink: 'Home Link',
    defaultHomeLink: 'Default is the system built-in home page',
    showFoot: 'Show login page footer',
    footContent: 'Foot content',
    webFormat: 'Please enter the correct URL starting with [https:// or http://]'
  },
  auth: {
    no_item_selected: 'Please select organization、user or role on the left',
    no_resource_selected: 'Please select resource on the left',
    search_pre: 'Search by ',
    search_suf: ' name',
    separate_auth: 'Separate Authorization',
    auth_extent_tips: 'Permissions Inherited From The Following Organizations Or Roles:',
    authConfig: 'Configure Permissions By User',
    sourceConfig: 'Configure Permissions By Source',
    authQuickConfig: 'Auth Quick Config',
    dept: 'Dept',
    role: 'Role',
    user: 'User',
    set_rules: 'Set rules',
    sysParams: 'System variable',
    sysParams_type: {
      user_id: 'User ID',
      user_name: 'User Name',
      user_source: 'User From',
      user_email: 'Email',
      dept: 'Dept',
      role: 'Role'
    },
    linkAuth: 'Datasource',
    datasetAuth: 'Dataset',
    chartAuth: 'Chart',
    panelAuth: 'Panel',
    menuAuth: 'Menu And Operation',
    deptHead: 'All Dept',
    roleHead: 'All Role',
    userHead: 'All User',
    linkAuthHead: 'All Datasource',
    datasetAuthHead: 'All Dataset',
    chartAuthHead: 'All Chart',
    panelAuthHead: 'All Chart',
    menuAuthHead: 'All menus and actions',
    view: 'View',
    use: 'Use',
    export: 'Export',
    manage: 'Manage',
    row_column: 'Row and column permission settings',
    row_permission: 'Row permission rules',
    enable_row: 'Enable row permissions',
    white_list: 'White list',
    white_user_not: 'The above permission rules do not take effect for white list users',
    organization_or_role: 'Please select an organization or role',
    column_permission: 'Column permission rule',
    enable_column: 'Enable column permissions',
    search_by_field: 'Search by field name',
    add_condition: 'Add condition',
    add_relationship: 'Add relationship',
    filter_fields: 'Filter fields',
    selct_filter_fields: 'Please select a filter field',
    enter_keywords: 'Please enter keywords',
    screen_method: 'Screening method',
    select: 'Please select',
    fixed_value: 'Fixed value',
    default_method: 'Default condition',
    select_all: 'Select all',
    added: 'Added',
    manual_input: 'Manual input',
    please_fill: 'Please fill in one line and add 500 at most. Duplicate options and added options will be automatically filtered when identifying and entering',
    close: 'close',
    add: 'add to',
    sure: 'determine'
  },
  about: {
    auth_to: 'Authorized to',
    invalid_license: 'Invalid License',
    update_license: 'Update License',
    expiration_time: 'Expiration Time',
    expirationed: '(Expired)',
    auth_num: 'Authorized quantity',
    version: 'Version',
    version_num: 'Version number',
    standard: 'Standard',
    enterprise: 'Enterprise',
    support: 'Get technical support',
    update_success: 'Update Success',
    serial_no: 'Serial Number',
    remark: 'Remark'
  },
  template: {
    exit_same_template_check: 'The Same Name Exists In Now Class. Do You Want To Override It?',
    override: 'Override',
    cancel: 'Cancel',
    confirm_upload: 'Upload Confirm'
  },
  cron: {
    second: 'Second',
    minute: 'Minute',
    hour: 'Hour',
    day: 'Day',
    minute_default: 'Minutes (execution time: 0 seconds)',
    hour_default: 'Hours (execution time: 0 minutes 0 seconds)',
    day_default: 'Day (execution time: 0:00:00)',
    month: 'Month',
    week: 'Week',
    year: 'Year',
    d_w_cant_not_set: 'Day and Week can not same as "Not set"',
    d_w_must_one_set: 'Day and Week at least on as "Not set"',
    every_day: 'Every day',
    cycle: 'Cycle',
    not_set: 'Not set',
    from: 'From',
    to: 'To',
    repeat: 'Repeat',
    day_begin: 'begin,every',
    day_exec: 'execute once',
    work_day: 'weekday',
    this_month: 'This month',
    day_near_work_day: 'nearly weekday',
    this_week_last_day: 'this month last day',
    set: 'Set',
    every_hour: 'Every hour',
    hour_begin: 'begin,every',
    hour_exec: 'execute once',
    every_month: 'Every month',
    month_begin: 'begin,every',
    month_exec: 'execute once',
    every: 'Every',
    every_begin: 'begin,every',
    every_exec: 'execute once',
    every_week: 'Every week',
    week_start: 'From week',
    week_end: 'to week',
    every_year: 'Every year',
    week_tips: 'Tips：1-7 mapping SUN-SAT',
    minute_limit: 'Minutes cannot be less than 1 and greater than 59',
    hour_limit: 'Hours cannot be less than 1 and greater than 23',
    day_limit: 'Days cannot be less than 1 and greater than 31'
  },
  dept: {
    can_not_move_change_sort: 'Cannot move to change sort',
    can_not_move_parent_to_children: 'Parent organization cannot move to its own child node',
    move_success: 'Mobile success',
    name_exist_pre: 'already existed organization named [',
    name_exist_suf: ']',
    root_org: 'Top organization'
  },
  webmsg: {
    web_msg: 'On site message notification',
    show_more: 'View more',
    all_type: 'All type',
    panel_type: 'Panel Share',
    dataset_type: 'Dataset sync',
    content: 'Content',
    sned_time: 'Send Time',
    read_time: 'Read Time',
    type: 'Message Type',
    mark_read: 'Mark As Read',
    all_mark_read: 'Mark All As Read',
    please_select: 'Please select at least one message',
    mark_success: 'Mark read successfully',
    receive_manage: 'Receive Manage',
    i18n_msg_type_panel_share: 'Dashboard sharing',
    i18n_msg_type_panel_share_cacnel: 'Dashboard unshared',
    i18n_msg_type_dataset_sync: 'Data set synchronization',
    i18n_msg_type_dataset_sync_success: 'Dataset synchronization successful',
    i18n_msg_type_dataset_sync_failed: 'Dataset synchronization failed',
    i18n_msg_type_all: 'All type',
    i18n_msg_type_ds_invalid: 'Datasource invalid',
    channel_inner_msg: 'On site',
    channel_email_msg: 'Email',
    channel_wecom_msg: 'Wecom',
    channel_dingtalk_msg: 'Dingtalk',
    channel_lark_msg: 'Lark',
    channel_larksuite_msg: 'INT Lark'
  },
  denumberrange: {
    label: 'Number range',
    split_placeholder: 'To',
    please_key_min: 'Please key min value',
    please_key_max: 'Please key max value',
    out_of_min: 'The min value cannot be less than the min integer -2³²',
    out_of_max: 'The max value cannot be more than the max integer 2³²-1',
    must_int: 'Please key integer',
    min_out_max: 'The min value must be less than the max value',
    max_out_min: 'The max value must be more than the min value'
  },
  desearchbutton: {
    label: 'Search Button',
    text: 'Text',
    auto_trigger: 'Auto Trigger',
    range: 'Range',
    relative: 'Relation',
    auto_trigger_tip: 'Automatically trigger once when entering the preview page',
    range_tip: 'Default association all filter components'
  },
  desresetbutton: {
    label: 'Clear',
    reset: 'Reset',
    text: 'Text'
  },
  denumberselect: {
    label: 'Number selector',
    placeholder: 'Please select number'
  },
  deinputsearch: {
    label: 'Text search',
    placeholder: 'Please key keyword'
  },
  detextselect: {
    label: 'Text selector',
    placeholder: 'Please select text'
  },
  detextselectTree: {
    label: 'Tree selector',
    placeholder: 'Please select'
  },
  detextgridselect: {
    label: 'Text list',
    placeholder: 'Please select'
  },
  denumbergridselect: {
    label: 'Number list',
    placeholder: 'Please select'
  },
  dedaterange: {
    label: 'Date range',
    to_placeholder: 'End date',
    from_placeholder: 'Start date',
    split_placeholder: 'To'
  },
  dedate: {
    label: 'Date',
    placeholder: 'Please select date'
  },
  deyearmonth: {
    label: 'Month',
    placeholder: 'Please select month'
  },
  deyear: {
    label: 'Year',
    placeholder: 'Please select year'
  },
  deshowdate: {
    label: 'Time',
    show_week: 'Show week',
    show_date: 'Show date',
    time_format: 'Time format',
    date_format: 'Date format',
    custom: 'Custom format',
    open_mode: 'Time category',
    m_default: 'Default',
    m_elec: 'Electronic clock',
    m_simple: 'Simple clock',
    m_complex: 'Complex clock',
    select_openMode: 'Please select time category',
    select_time_format: 'Please select time format',
    select_date_format: 'Please select date format'

  },
  xpacktask: {
    add: 'Add task',
    edit: 'Edit task',
    task_id: 'Task id',
    name: 'Name',
    last_exec_time: 'Last execute time',
    last_exec_status: 'Last execute status',
    ready: 'Ready',
    success: 'Success',
    underway: 'Underway',
    error: 'Error',
    creator: 'Creator',
    create_time: 'Create time',
    search_by_name: 'Search by name',
    exec_time: 'Execute time',
    status: 'Execute status',
    task_status: 'Task status',
    running: 'Running',
    stopped: 'Stopped',
    start: 'Start',
    start_success: 'Start success',
    start_success_but: ', But the task expired, please manually change the end time',
    sure_batch_delete: 'Are you sure you want to delete tasks in bulk?',
    pixel_error: 'Pixel only support {800 - 10000} * {500 - 6250}',
    next_exec_time: 'Next execute time'

  },
  emailtask: {
    week_mon: 'Mon',
    week_tue: 'Tue',
    week_wed: 'Wed',
    week_thu: 'Thu',
    week_fri: 'Fri',
    week_sat: 'Sat',
    week_sun: 'Sun',
    send_config: 'Send configuration',
    title: 'Title',
    panel: 'Panel',
    content: 'Content',
    recipients: 'Recipients',
    recisetting: 'channel',
    email: 'Email',
    wecom: 'Wecom',
    dingtalk: 'Dingtalk',
    lark: 'Lark',
    larksuite: 'INT Lark',
    pixel: 'Pixel',
    default: 'Default',
    custom: 'Custom',
    rate_type: 'Rate type',
    cron_exp: 'Cron expression',
    exec_time: 'Execute time',
    start_time: 'Start time',
    end_time: 'End time',
    chart_data: 'View data',
    panel_preview: 'Preview report',
    preview: 'Preview',
    emial_preview: 'Report preview',
    chart_data_range: 'View data range',
    simple_repeat: 'Simple repeat',
    once_a_day: 'Once a day',
    once_a_week: 'Once a week',
    once_a_month: 'Once a month',
    complex_repeat: 'Complex repeat',
    pixel_tip: 'Please code custom pixel(such as 2560 * 1600) or select',
    task_type: 'Task type',
    range_view: 'Displayed data',
    range_all: 'All data',
    execute_now: 'Execute now',
    fire_now_success: 'Task executing'
  },
  dynamic_time: {
    set_default: 'Set Default',
    fix: 'Fixed Time',
    dynamic: 'Dynamic Time',
    relative: 'Relative Current',
    today: 'Today',
    yesterday: 'Yesterday',
    firstOfMonth: 'Month Beginning',
    firstOfYear: 'Year Beginning',
    custom: 'Custom',
    date: 'date',
    week: 'week',
    month: 'Month',
    year: 'Year',
    before: 'Before',
    after: 'After',
    preview: 'Preview',
    set: 'Set',

    cweek: 'This Week',
    lweek: 'Last Week',
    cmonth: 'This Month',
    cquarter: 'This Quarter',
    lquarter: 'Last Quarter',
    cyear: 'This Year'
  },
  dynamic_year: {
    fix: 'Fixed Year',
    dynamic: 'Dynamic Year',
    current: 'This Year',
    last: 'Last Year'
  },
  dynamic_month: {
    fix: 'Fixed Month',
    dynamic: 'Dynamic Month',
    current: 'This Month',
    last: 'Last Month',
    firstOfYear: 'First month of this year',
    sameMonthLastYear: 'Same month last year'
  },
  wizard: {
    welcome_title: 'Welcome Use DataEase',
    welcome_hint: 'Easy open-source data visualization and analytics tool',
    demo_video: 'Demo',
    quick_start: 'Quick Start',
    online_document: 'Online',
    latest_developments: 'Latest',
    teaching_video: 'Teaching',
    enterprise_edition: 'Enterprise',
    contact_us: 'Contact Us',
    demo_video_hint: 'How to make a DataEase dashboard in 3 minutes and share it with others',
    online_document_hint: 'It covers the installation steps, user manuals, tutorials, solutions to common problems, and secondary development of DataEase',
    teaching_video_bottom_hint: 'More videos',
    enterprise_edition_hint1: 'Provide enterprise application scenario X-Pack enhancement package',
    enterprise_edition_hint2: 'Provide high-level original factory service support',
    enterprise_edition_hint3: 'Provide DataEase best practice recommendations',
    open_source_community: 'Open source community',
    click_show: 'Click To View',
    show_more: 'Show More',
    click_inner: 'Click To Enter',
    email: 'Email:',
    tel: 'Tel:',
    web: 'Web:',
    apply: 'Free Trial Application',
    more: 'More',
    weChat_official_account: 'WeChat official account',
    technical_group: 'Technical exchange group',
    f2c_train: 'FIT2CLOUD Certification'
  },
  kettle: {
    add: 'Add Kettle',
    status: 'Status',
    carte: 'Kettle Address',
    port: 'Port',
    user: 'User',
    passwd: 'Password'
  },
  log: {
    title: 'Operate Log',
    optype: 'Operate Type',
    detail: 'Detail',
    user: 'User',
    time: 'Time',
    export: 'Export',
    export_as: 'Export as',
    confirm: 'Sure Export ?',
    search_by_key: 'Search by key',
    ip: 'IP'
  },
  plugin_style: {
    border: 'Border'
  },
  sql_variable: {
    variable_mgm: 'Parameter setting'
  },
  map_setting: {
    area_level: 'Area Level',
    area_code: 'Area Code',
    please_input: 'please key',
    parent_area: 'Parent Area',
    area_code_tip: 'The format of area code is 9 digits',
    area_name: 'Area Name',
    parent_name: 'Parent Area',
    geo_json: 'Geo Json',
    fileplaceholder: 'Please upload the JSON format coordinate file',
    delete_confirm: 'And child nodes will be deleted. Confirm to execute ?',
    cur_node: 'Current node'
  },
  map_mapping: {
    map: 'Map',
    attr: 'Attribute',
    empty: 'Empty',
    please_select_map: 'Please select a range of map'
  },
  'I18N_USER_TEMPLATE_ERROR': 'Template file error',
  'i18n_max_user_import_size': 'File size exceeds 10M',
  app_template: {
    move: 'Move',
    move_item: 'Move App',
    datasource_new: 'New',
    datasource_history: 'Multiplexing',
    datasource_from: 'Datasource From',
    apply_template: 'Apply template',
    execution_time: 'Execution time',
    app_manager: 'Application management',
    app_upload: 'Upload app',
    no_apps: 'No apps',
    app_group_icon: 'Cover icon',
    app_name: 'Application name',
    search_by_keyword: 'Search by keyword',
    apply_logs: 'Apply logs',
    app_group_delete_tips: 'Are you sure to delete this application category?',

    app_group_delete_content: 'After deletion, all application templates in this category will also be deleted.',
    panel_position: 'Panel position',
    panel_name: 'Panel name',
    dataset_group_position: 'Dataset group position',
    dataset_group_name: 'Dataset name',
    datasource_info: 'Datasource info',
    datasource: 'Datasource',
    dataset_group: 'Dataset group',
    panel: 'Panel',
    log_delete_tips: 'Are you sure to delete this application record?',
    log_resource_delete_tips: 'Delete related resources (irrecoverable after deletion)',
    file_error_tips: 'The relevant data file is not found. The current file may not be a DataEase application file, or the file may be damaged ',
    app_export: 'Application export',
    app_version: 'Application version',
    program_version: 'DataEase minimum version',
    creator: 'Author',
    export: 'Export'
  },

  logout: {
    oidc_logout_error: 'OIDC failed to exit, do you continue to exit DataEase?',
    cas_logout_error: 'The CAS service is abnormal, please contact the administrator!'
  },
  watermark: {
    support_params: 'Currently supported parameters:',
    enable: 'Enable',
    enable_panel_custom: 'Allow the dashboard to open or close the watermark independently',
    content: 'content',
    custom_content: 'Custom Content',
    account: 'Account',
    nick_name: 'Nick Name',
    ip: 'IP',
    now: 'Now Time',
    watermark_color: 'Watermark Color',
    watermark_font_size: 'Watermark Fontsize',
    watermark_space: 'Watermark Space',
    horizontal: 'Horizontal Space',
    vertical: 'Vertical Space',
    reset: 'Reset',
    preview: 'Preview',
    save: 'Save'
  }
}
