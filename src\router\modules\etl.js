/** When your routing table is too long, you can split it into small modules **/

import Layout from '@/layout'

const etlRouter = {
  path: '/etl',
  component: Layout,
  redirect: '/etl/job',
  name: 'Etl',
  meta: {
    title: 'ETL数据集成',
    icon: 'el-icon-data-line'
  },
  children: [
    {
      path: 'job',
      component: () => import('@/views/etl/job/index'),
      name: 'EtlJob',
      meta: { title: 'ETL作业管理' }
    },
    {
      path: 'schedule',
      component: () => import('@/views/etl/schedule/index'),
      name: 'EtlSchedule',
      meta: { title: '作业调度管理' }
    },
    {
      path: 'transform',
      component: () => import('@/views/etl/transform/index'),
      name: 'EtlTransform',
      meta: { title: '数据转换管理' }
    },
    {
      path: 'transform/design',
      component: () => import('@/views/etl/transform/components/design'),
      name: 'TransformDesign',
      meta: { 
        title: '转换设计',
        activeMenu: '/etl/transform'
      },
      hidden: true
    },
    {
      path: 'job/design',
      component: () => import('@/views/etl/job/components/design'),
      name: 'JobDesign',
      meta: { 
        title: '作业设计',
        activeMenu: '/etl/job'
      },
      hidden: true
    },
    {
      path: 'monitor',
      component: () => import('@/views/etl/monitor/index'),
      name: 'JobMonitor',
      meta: { 
        title: '作业监控',
        icon: 'monitor'
      }
    },
    {
      path: 'log',
      component: () => import('@/views/etl/log/index'),
      name: 'JobLog',
      meta: { 
        title: '作业日志',
        icon: 'log'
      }
    }
  ]
}

export default etlRouter
