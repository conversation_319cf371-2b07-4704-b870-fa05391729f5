import request from '@/utils/request'

// 查询数据集列表
export function listDataset(query) {
  return request({
    url: '/report/dataset/list',
    method: 'get',
    params: query
  })
}

// 查询数据集详细信息
export function getDataset(id) {
  return request({
    url: '/report/dataset/get',
    method: 'get',
    params: { id }
  })
}

// 新增数据集
export function addDataset(data) {
  return request({
    url: '/report/dataset/add',
    method: 'post',
    data: data
  })
}

// 修改数据集
export function updateDataset(data) {
  return request({
    url: '/report/dataset/update',
    method: 'put',
    data: data
  })
}

// 删除数据集
export function delDataset(id) {
  return request({
    url: '/report/dataset/delete',
    method: 'delete',
    params: { id }
  })
}

// 获取数据集数据
export function getDatasetData(id, params) {
  return request({
    url: `/report/dataset/data/${id}`,
    method: 'get',
    params
  })
}

// 测试数据集连接
export function testDatasetConnection(data) {
  return request({
    url: '/report/dataset/test-connection',
    method: 'post',
    data: data
  })
}

// 获取数据集字段
export function getDatasetFields(id) {
  return request({
    url: `/report/dataset/fields/${id}`,
    method: 'get'
  })
}

// 同步数据集结构
export function syncDatasetSchema(id) {
  return request({
    url: `/report/dataset/sync/${id}`,
    method: 'post'
  })
}

// 查询数据集类型列表
export function listDatasetTypes() {
  return request({
    url: '/report/dataset/types',
    method: 'get'
  })
}

// 根据数据源ID获取可用的表
export function getTablesFromDataSource(datasourceId) {
  return request({
    url: `/report/dataset/tables/${datasourceId}`,
    method: 'get'
  })
}

// 根据数据源ID和表名获取表字段
export function getTableFields(datasourceId, tableName) {
  return request({
    url: `/report/dataset/fields/${datasourceId}/${tableName}`,
    method: 'get'
  })
} 