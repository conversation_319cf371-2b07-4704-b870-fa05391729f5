import store from '@/store'
import { hasPermission, hasRole, hasReportPermission, hasReportCategoryPermission } from '@/utils/permission'

export default {
  data() {
    return {
      // 权限判断指令
      access: {
        // 系统权限
        system: {
          view: false,
          edit: false,
          remove: false,
          add: false,
          import: false,
          export: false
        },
        // 报表权限
        report: {
          view: false,
          edit: false,
          remove: false,
          add: false,
          import: false,
          export: false,
          share: false
        },
        // 报表分类权限
        category: {
          view: false,
          edit: false,
          remove: false,
          add: false
        }
      }
    }
  },
  watch: {
    // 监听reportId变化
    reportId: {
      handler(newVal) {
        if (newVal) {
          this.updateReportAccess(newVal)
        }
      },
      immediate: true
    },
    // 监听categoryId变化
    categoryId: {
      handler(newVal) {
        if (newVal) {
          this.updateCategoryAccess(newVal)
        }
      },
      immediate: true
    }
  },
  created() {
    this.initAccess()
  },
  methods: {
    // 初始化权限
    initAccess() {
      // 设置系统权限
      this.access.system = {
        view: hasPermission('system:view'),
        edit: hasPermission('system:edit'),
        remove: hasPermission('system:delete'),
        add: hasPermission('system:add'),
        import: hasPermission('system:import'),
        export: hasPermission('system:export')
      }

      // 设置全局报表权限
      this.access.report = {
        view: hasPermission('report:view'),
        edit: hasPermission('report:edit'),
        remove: hasPermission('report:delete'),
        add: hasPermission('report:add'),
        import: hasPermission('report:import'),
        export: hasPermission('report:export'),
        share: hasPermission('report:share')
      }

      // 设置全局分类权限
      this.access.category = {
        view: hasPermission('report:category:view'),
        edit: hasPermission('report:category:edit'),
        remove: hasPermission('report:category:delete'),
        add: hasPermission('report:category:add')
      }

      // 如果有reportId，更新报表权限
      if (this.reportId) {
        this.updateReportAccess(this.reportId)
      }

      // 如果有categoryId，更新分类权限
      if (this.categoryId) {
        this.updateCategoryAccess(this.categoryId)
      }
    },
    // 更新报表权限
    updateReportAccess(reportId) {
      this.access.report = {
        ...this.access.report,
        view: hasReportPermission(reportId, 'view'),
        edit: hasReportPermission(reportId, 'edit'),
        remove: hasReportPermission(reportId, 'delete'),
        export: hasReportPermission(reportId, 'export'),
        share: hasReportPermission(reportId, 'share')
      }
    },
    // 更新分类权限
    updateCategoryAccess(categoryId) {
      this.access.category = {
        ...this.access.category,
        view: hasReportCategoryPermission(categoryId, 'view'),
        edit: hasReportCategoryPermission(categoryId, 'edit'),
        remove: hasReportCategoryPermission(categoryId, 'delete')
      }
    },
    // 检查系统权限
    hasPermission(permission) {
      return hasPermission(permission)
    },
    // 检查角色权限
    hasRole(role) {
      return hasRole(role)
    },
    // 检查报表权限
    hasReportPermission(reportId, action) {
      return hasReportPermission(reportId, action)
    },
    // 检查报表分类权限
    hasReportCategoryPermission(categoryId, action) {
      return hasReportCategoryPermission(categoryId, action)
    }
  }
} 