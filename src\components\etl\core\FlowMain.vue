<template>
  <div class="flow-main"
    @dragover="handleDragOver"
    @drop="handleDrop"
    @click="handleCanvasClick">
    <div class="flow-canvas">
      <flow-node
        v-for="node in nodes"
        :key="node.id"
        :node="node"
        :selected="selectedNode === node"
        @select="handleNodeSelect"
        @update:position="updateNodePosition"
        @connect-start="startConnection"
        @connect-end="endConnection" />
      <!-- 连接线 -->
      <svg class="flow-connections">
        <path
          v-for="conn in connections"
          :key="conn.id"
          :d="getConnectionPath(conn)"
          :class="['connection-path', {'selected': selectedConnection === conn}]"
          @click="handleConnectionClick(conn)" />
      </svg>
      <!-- 临时连接线 -->
      <svg class="temp-connection" v-if="tempConnection">
        <path
          :d="getTempConnectionPath()"
          class="temp-connection-path" />
      </svg>
    </div>
  </div>
</template>

<script>
import FlowNode from './FlowNode.vue'
import { v4 as uuidv4 } from 'uuid'

export default {
  name: 'FlowMain',
  components: {
    FlowNode
  },
  props: {
    type: {
      type: String,
      required: true,
      validator: value => ['job', 'trans'].includes(value)
    }
  },
  data() {
    return {
      nodes: [],
      connections: [],
      selectedNode: null,
      selectedConnection: null,
      tempConnection: null,
      sourceNode: null,
      scale: 1,
      pan: { x: 0, y: 0 }
    }
  },
  methods: {
    handleDragOver(event) {
      event.preventDefault()
      event.dataTransfer.dropEffect = 'copy'
    },
    handleDrop(event) {
      event.preventDefault()
      const component = this.$parent.dragComponent
      if (!component) return

      const rect = event.currentTarget.getBoundingClientRect()
      const x = event.clientX - rect.left
      const y = event.clientY - rect.top

      this.addNode({
        ...component,
        left: x,
        top: y
      })
      this.$parent.dragComponent = null
    },
    addNode(nodeData) {
      const node = {
        id: nodeData.id || uuidv4(),
        type: nodeData.type,
        name: nodeData.name,
        icon: nodeData.icon,
        left: nodeData.left,
        top: nodeData.top,
        inputs: [],
        outputs: []
      }
      this.nodes.push(node)
    },
    handleNodeSelect(node) {
      this.selectedNode = node
      this.selectedConnection = null
      this.$emit('nodeSelect', node)
    },
    updateNodePosition({ id, left, top }) {
      const node = this.nodes.find(n => n.id === id)
      if (node) {
        node.left = left
        node.top = top
      }
    },
    startConnection(data) {
      this.sourceNode = data.node
      this.tempConnection = {
        sourceX: data.x,
        sourceY: data.y,
        targetX: data.x,
        targetY: data.y
      }

      const moveHandler = (e) => {
        const rect = this.$el.getBoundingClientRect()
        this.tempConnection.targetX = e.clientX - rect.left
        this.tempConnection.targetY = e.clientY - rect.top
      }

      const upHandler = () => {
        document.removeEventListener('mousemove', moveHandler)
        document.removeEventListener('mouseup', upHandler)
        this.tempConnection = null
      }

      document.addEventListener('mousemove', moveHandler)
      document.addEventListener('mouseup', upHandler)
    },
    endConnection(targetNode) {
      if (this.sourceNode && this.sourceNode !== targetNode) {
        this.addConnection(this.sourceNode, targetNode)
      }
      this.sourceNode = null
      this.tempConnection = null
    },
    addConnection(source, target) {
      // 检查是否已存在连接
      const exists = this.connections.some(conn => 
        conn.source === source.id && conn.target === target.id
      )
      if (exists) return

      this.connections.push({
        id: uuidv4(),
        source: source.id,
        target: target.id
      })

      // 更新节点的输入输出信息
      source.outputs.push(target.id)
      target.inputs.push(source.id)
    },
    handleConnectionClick(connection) {
      this.selectedConnection = connection
      this.selectedNode = null
    },
    handleCanvasClick(event) {
      if (event.target === event.currentTarget) {
        this.selectedNode = null
        this.selectedConnection = null
        this.$emit('nodeSelect', null)
      }
    },
    getConnectionPath(connection) {
      const source = this.nodes.find(n => n.id === connection.source)
      const target = this.nodes.find(n => n.id === connection.target)
      if (!source || !target) return ''

      return this.calculatePath(
        source.left + 100, // 节点宽度
        source.top + 40,  // 节点高度的一半
        target.left,
        target.top + 40
      )
    },
    getTempConnectionPath() {
      if (!this.tempConnection) return ''
      return this.calculatePath(
        this.tempConnection.sourceX,
        this.tempConnection.sourceY,
        this.tempConnection.targetX,
        this.tempConnection.targetY
      )
    },
    calculatePath(x1, y1, x2, y2) {
      const offset = 50
      const midX = x1 + (x2 - x1) / 2
      return `M ${x1} ${y1} C ${midX} ${y1}, ${midX} ${y2}, ${x2} ${y2}`
    },
    getFlowData() {
      return {
        nodes: this.nodes,
        connections: this.connections
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.flow-main {
  width: 100%;
  height: 100%;
  position: relative;
  overflow: hidden;
  background-color: #f0f2f5;
  
  .flow-canvas {
    width: 100%;
    height: 100%;
    position: relative;
  }

  .flow-connections {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;

    .connection-path {
      fill: none;
      stroke: #666;
      stroke-width: 2px;
      pointer-events: stroke;

      &.selected {
        stroke: #409eff;
        stroke-width: 3px;
      }

      &:hover {
        stroke: #409eff;
      }
    }
  }

  .temp-connection {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;

    .temp-connection-path {
      fill: none;
      stroke: #409eff;
      stroke-width: 2px;
      stroke-dasharray: 5,5;
    }
  }
}
</style>
