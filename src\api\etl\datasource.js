import request from '@/utils/request'

export function dsGrid(pageIndex, pageSize, data) {
  return request({
    url: '/etl/datasource/list/' + pageIndex + '/' + pageSize,
    method: 'post',
    loading: true,
    data
  })
}
export function listDatasource() {
  return request({
    url: '/etl/datasource/list',
    loading: true,
    method: 'get'
  })
}
export function listDrivers() {
  return request({
    url: '/etl/driver/list',
    loading: true,
    method: 'post'
  })
}
export function listDatasourceType() {
  return request({
    url: '/etl/datasource/types',
    loading: true,
    method: 'get'
  })
}
export function listDatasourceByType(type) {
  return request({
    url: '/etl/datasource/list/' + type,
    loading: true,
    method: 'get'
  })
}
export function listDriverByType(type) {
  return request({
    url: '/etl/driver/list/' + type,
    loading: true,
    method: 'get'
  })
}

export function addDs(data) {
  return request({
    url: '/etl/datasource/add/',
    method: 'post',
    loading: true,
    data
  })
}

export function editDs(data) {
  return request({
    url: '/etl/datasource/update/',
    method: 'post',
    loading: true,
    data
  })
}

export function delDs(id) {
  return request({
    url: '/etldatasource/delete/' + id,
    loading: true,
    method: 'post'
  })
}

export function validateDs(data) {
  return request({
    url: '/etl/datasource/validate/',
    method: 'post',
    loading: true,
    data
  })
}

export function validateDsById(datasourceId) {
  return request({
    url: '/etl/datasource/validate/' + datasourceId,
    method: 'get',
    loading: true
  })
}

export function getSchema(data) {
  return request({
    url: '/etl/datasource/getSchema/',
    method: 'post',
    loading: true,
    data
  })
}

export function checkApiDatasource(data) {
  return request({
    url: '/etl/datasource/checkApiDatasource',
    method: 'post',
    loading: false,
    data
  })
}

export function addDriver(data) {
  return request({
    url: '/etl/driver/save',
    method: 'post',
    loading: true,
    data
  })
}

export function listDriverDetails(id) {
  return request({
    url: '/etl/driver/listDriverDetails/' + id,
    method: 'get',
    loading: true
  })
}

export function deleteDriverFile(data) {
  return request({
    url: '/etl/driver/deleteDriverFile',
    method: 'post',
    loading: true,
    data
  })
}

export function delDriver(data) {
  return request({
    url: '/etl/driver/delete',
    loading: true,
    method: 'post',
    data
  })
}
export function updateDriver(data) {
  return request({
    url: '/etl/driver/update/',
    loading: true,
    method: 'post',
    data
  })
}

export function getDatasourceDetail(id) {
  return request({
    url: `/etl/datasource/get/${id}`,
    loading: true,
    method: 'post'
  })
}
export default { getDatasourceDetail, dsGrid, addDs, editDs, delDs, validateDs, listDatasource, getSchema }
