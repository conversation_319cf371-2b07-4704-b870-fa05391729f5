import { getAccessibleOAuth2Apps } from '@/api/system/oauth2/oauth2Client'

const state = {
  accessibleApps: [],
  loading: false,
  userPermissions: [] // 存储用户权限信息
}

const mutations = {
  SET_ACCESSIBLE_APPS: (state, apps) => {
    state.accessibleApps = apps
  },
  SET_LOADING: (state, status) => {
    state.loading = status
  },
  SET_USER_PERMISSIONS: (state, permissions) => {
    state.userPermissions = permissions
  }
}

const actions = {
  // 获取可访问的OAuth2应用列表
  getAccessibleApps({ commit, dispatch }) {
    commit('SET_LOADING', true)
    return new Promise((resolve, reject) => {
      getAccessibleOAuth2Apps()
        .then(response => {
          const { data } = response
          commit('SET_ACCESSIBLE_APPS', data)
          resolve(data)
        })
        .catch(error => {
          reject(error)
        })
        .finally(() => {
          commit('SET_LOADING', false)
        })
    })
  }
}

const getters = {
  // 获取应用列表 - 已按权限过滤
  appList: state => {
    return state.accessibleApps || []
  },
  
  // 是否有可访问的应用
  hasApps: state => {
    return state.accessibleApps && state.accessibleApps.length > 0
  },
  
  // 加载状态
  isLoading: state => {
    return state.loading
  }
}

export default {
  namespaced: true,
  state,
  mutations,
  actions,
  getters
} 