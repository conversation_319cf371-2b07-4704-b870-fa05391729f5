<template>
  <div class="merge-join-config">
    <el-form label-position="top" size="small">
      <el-form-item label="第一个数据流">
        <el-select v-model="config.firstStep" placeholder="选择步骤">
          <el-option
            v-for="step in previousSteps"
            :key="step.id"
            :label="step.name"
            :value="step.id"
          />
        </el-select>
      </el-form-item>

      <el-form-item label="第二个数据流">
        <el-select v-model="config.secondStep" placeholder="选择步骤">
          <el-option
            v-for="step in previousSteps"
            :key="step.id"
            :label="step.name"
            :value="step.id"
          />
        </el-select>
      </el-form-item>

      <el-form-item label="连接类型">
        <el-select v-model="config.joinType">
          <el-option label="INNER JOIN" value="INNER" />
          <el-option label="LEFT OUTER JOIN" value="LEFT OUTER" />
          <el-option label="RIGHT OUTER JOIN" value="RIGHT OUTER" />
          <el-option label="FULL OUTER JOIN" value="FULL OUTER" />
        </el-select>
      </el-form-item>

      <el-form-item label="连接条件">
        <div class="join-keys">
          <div class="keys-table">
            <div class="header">
              <div class="col">第一个数据流字段</div>
              <div class="col">第二个数据流字段</div>
              <div class="col">操作</div>
            </div>
            <div 
              v-for="(key, index) in config.keys" 
              :key="index"
              class="row">
              <div class="col">
                <el-select 
                  v-model="key.firstField"
                  placeholder="选择字段">
                  <el-option
                    v-for="field in firstStepFields"
                    :key="field.name"
                    :label="field.name"
                    :value="field.name"
                  />
                </el-select>
              </div>
              <div class="col">
                <el-select 
                  v-model="key.secondField"
                  placeholder="选择字段">
                  <el-option
                    v-for="field in secondStepFields"
                    :key="field.name"
                    :label="field.name"
                    :value="field.name"
                  />
                </el-select>
              </div>
              <div class="col">
                <el-button
                  type="text"
                  icon="el-icon-delete"
                  @click="removeKey(index)"
                />
              </div>
            </div>
          </div>
          <el-button
            type="text"
            icon="el-icon-plus"
            @click="addKey">
            添加连接条件
          </el-button>
        </div>
      </el-form-item>

      <el-form-item>
        <el-checkbox v-model="config.sortedMerge">数据已经排序</el-checkbox>
      </el-form-item>

      <template v-if="!config.sortedMerge">
        <el-alert
          title="如果数据未排序，系统将自动对数据进行排序"
          type="warning"
          :closable="false"
          show-icon
        />
      </template>
    </el-form>
  </div>
</template>

<script>
export default {
  name: 'MergeJoin',
  props: {
    value: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      config: {
        firstStep: '',
        secondStep: '',
        joinType: 'INNER',
        keys: [],
        sortedMerge: false
      },
      previousSteps: [],
      firstStepFields: [],
      secondStepFields: []
    }
  },
  watch: {
    value: {
      handler(val) {
        this.config = { ...this.config, ...val }
      },
      immediate: true,
      deep: true
    },
    config: {
      handler(val) {
        this.$emit('input', val)
      },
      deep: true
    },
    'config.firstStep': {
      async handler(val) {
        if (val) {
          await this.loadFirstStepFields()
        }
      }
    },
    'config.secondStep': {
      async handler(val) {
        if (val) {
          await this.loadSecondStepFields()
        }
      }
    }
  },
  methods: {
    addKey() {
      this.config.keys.push({
        firstField: '',
        secondField: ''
      })
    },
    removeKey(index) {
      this.config.keys.splice(index, 1)
    },
    async loadFirstStepFields() {
      try {
        if (!this.config.firstStep) return
        // TODO: 获取第一个步骤的输出字段
      } catch (error) {
        this.$message.error('获取字段失败：' + error.message)
      }
    },
    async loadSecondStepFields() {
      try {
        if (!this.config.secondStep) return
        // TODO: 获取第二个步骤的输出字段
      } catch (error) {
        this.$message.error('获取字段失败：' + error.message)
      }
    }
  },
  async created() {
    try {
      // TODO: 获取前置步骤列表
    } catch (error) {
      console.error('获取步骤失败：', error)
    }
  }
}
</script>

<style lang="scss" scoped>
.merge-join-config {
  padding: 16px;

  .join-keys {
    border: 1px solid #dcdfe6;
    border-radius: 4px;
    padding: 16px;

    .keys-table {
      .header, .row {
        display: grid;
        grid-template-columns: 2fr 2fr 60px;
        gap: 8px;
        align-items: center;
        padding: 8px 0;
        
        &:not(:last-child) {
          border-bottom: 1px solid #ebeef5;
        }
      }

      .header {
        font-weight: bold;
      }

      .row {
        .col {
          display: flex;
          align-items: center;
          justify-content: center;

          .el-select {
            width: 100%;
          }
        }
      }
    }
  }

  .el-alert {
    margin-top: 16px;
  }
}
</style>
