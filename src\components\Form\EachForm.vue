<template>
  <div class="each-form-item">
    <!-- 输入框 -->
    <el-input
      v-if="item.type === 'input'"
      v-model="currentValue[item.key]"
      :placeholder="item.placeholder"
      :size="item.size || 'small'"
      :disabled="item.disabled"
      @change="handleChange"
    />

    <!-- 下拉选择框 -->
    <el-select
      v-else-if="item.type === 'select'"
      v-model="currentValue[item.key]"
      :placeholder="item.placeholder"
      :size="item.size || 'small'"
      :disabled="item.disabled"
      :multiple="item.multiple"
      :clearable="item.clearable"
      @change="handleChange"
    >
      <el-option
        v-for="option in item.options"
        :key="option.value"
        :label="option.label"
        :value="option.value"
      />
    </el-select>

    <!-- 日期选择器 -->
    <el-date-picker
      v-else-if="item.type === 'date'"
      v-model="currentValue[item.key]"
      :type="item.dateType || 'date'"
      :placeholder="item.placeholder"
      :size="item.size || 'small'"
      :disabled="item.disabled"
      :format="item.format"
      :value-format="item.valueFormat"
      @change="handleChange"
    />

    <!-- 数字输入框 -->
    <el-input-number
      v-else-if="item.type === 'number'"
      v-model="currentValue[item.key]"
      :min="item.min"
      :max="item.max"
      :step="item.step"
      :size="item.size || 'small'"
      :disabled="item.disabled"
      @change="handleChange"
    />

    <!-- 开关 -->
    <el-switch
      v-else-if="item.type === 'switch'"
      v-model="currentValue[item.key]"
      :active-text="item.activeText"
      :inactive-text="item.inactiveText"
      :disabled="item.disabled"
      @change="handleChange"
    />

    <!-- 单选框组 -->
    <el-radio-group
      v-else-if="item.type === 'radio'"
      v-model="currentValue[item.key]"
      :size="item.size || 'small'"
      :disabled="item.disabled"
      @change="handleChange"
    >
      <el-radio
        v-for="option in item.options"
        :key="option.value"
        :label="option.value"
      >{{ option.label }}</el-radio>
    </el-radio-group>

    <!-- 复选框组 -->
    <el-checkbox-group
      v-else-if="item.type === 'checkbox'"
      v-model="currentValue[item.key]"
      :size="item.size || 'small'"
      :disabled="item.disabled"
      @change="handleChange"
    >
      <el-checkbox
        v-for="option in item.options"
        :key="option.value"
        :label="option.value"
      >{{ option.label }}</el-checkbox>
    </el-checkbox-group>

    <!-- 文本域 -->
    <el-input
      v-else-if="item.type === 'textarea'"
      v-model="currentValue[item.key]"
      type="textarea"
      :rows="item.rows || 3"
      :placeholder="item.placeholder"
      :disabled="item.disabled"
      @change="handleChange"
    />

    <!-- 自定义组件 -->
    <component
      v-else-if="item.type === 'custom'"
      :is="item.component"
      v-model="currentValue[item.key]"
      v-bind="item.props"
      @change="handleChange"
    />
  </div>
</template>

<script>
export default {
  name: "EachForm",
  props: {
    item: {
      type: Object,
      required: true
    },
    value: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      currentValue: this.value
    }
  },
  watch: {
    value: {
      handler(val) {
        this.currentValue = val
      },
      deep: true
    }
  },
  methods: {
    handleChange(val) {
      this.$emit('input', this.currentValue)
      this.$emit('eachChange', {
        key: this.item.key,
        value: val,
        item: this.item
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.each-form-item {
  display: inline-block;
  margin-right: 10px;
  margin-bottom: 10px;

  .el-input,
  .el-select,
  .el-date-picker,
  .el-input-number {
    width: 200px;
  }

  .el-textarea {
    width: 300px;
  }
}
</style> 