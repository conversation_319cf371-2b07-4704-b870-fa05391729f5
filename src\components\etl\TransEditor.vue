<template>
  <div class="trans-editor">
    <div class="trans-header">
      <div class="left-tools">
        <el-button-group>
          <el-button type="primary" size="small" @click="saveTrans">保存</el-button>
          <el-button type="success" size="small" @click="runTrans">运行</el-button>
          <el-button type="warning" size="small" @click="stopTrans">停止</el-button>
        </el-button-group>
      </div>
      <div class="right-tools">
        <el-button-group>
          <el-button size="small" @click="previewTrans">预览</el-button>
          <el-button size="small" @click="debugTrans">调试</el-button>
          <el-button size="small" @click="verifyTrans">校验</el-button>
        </el-button-group>
      </div>
    </div>
    <div class="trans-content">
      <div class="left-panel">
        <div class="panel-title">转换组件</div>
        <div class="component-list">
          <el-collapse v-model="activeCategory">
            <el-collapse-item 
              v-for="(components, category) in transComponentList" 
              :key="category"
              :title="category"
              :name="category">
              <div 
                v-for="item in components"
                :key="item.type"
                class="component-item"
                draggable="true"
                @dragstart="handleDragStart(item)">
                <i :class="item.icon"></i>
                <span>{{ item.name }}</span>
              </div>
            </el-collapse-item>
          </el-collapse>
        </div>
      </div>
      <div class="center-panel">
        <flow-main 
          ref="flowMain"
          :type="'trans'"
          @nodeSelect="handleNodeSelect" />
      </div>
      <div class="right-panel" v-if="currentNode">
        <div class="panel-title">转换节点配置</div>
        <div class="panel-content">
          <el-form label-position="top" size="small">
            <el-form-item label="节点名称">
              <el-input v-model="currentNode.name" />
            </el-form-item>
            <el-form-item label="节点描述">
              <el-input type="textarea" v-model="currentNode.description" />
            </el-form-item>
          </el-form>

          <component
            :is="getStepComponent(currentNode.type)"
            v-if="currentNode.type"
            v-model="currentNode.config"
            @update:config="updateNodeConfig"
          />
        </div>
      </div>
    </div>
    <!-- 执行日志面板 -->
    <div class="trans-log" v-show="showLog">
      <div class="log-header">
        <span>执行日志</span>
        <el-button type="text" @click="showLog = false">关闭</el-button>
      </div>
      <div class="log-content">
        <pre>{{ executionLog }}</pre>
      </div>
    </div>
  </div>
</template>

<script>
import FlowMain from './core/FlowMain.vue'
import { v4 as uuidv4 } from 'uuid'
import { save, run, stop, verify, preview, debug, executeResult } from '@/api/etl/transform'

// 导入步骤组件
import TableInput from './steps/TableInput.vue'
import TableOutput from './steps/TableOutput.vue'
import FilterRows from './steps/FilterRows.vue'
import SortRows from './steps/SortRows.vue'
import MergeJoin from './steps/MergeJoin.vue'

export default {
  name: 'TransEditor',
  components: {
    FlowMain,
    TableInput,
    TableOutput,
    FilterRows,
    SortRows,
    MergeJoin
  },
  data() {
    return {
      activeCategory: ['输入', '输出', '转换', '脚本', '流程'],
      currentNode: null,
      dragComponent: null,
      showLog: false,
      executionLog: '',
      transComponentList: {
        '输入': [
          { type: 'TableInput', name: '表输入', icon: 'el-icon-folder-opened' },
          { type: 'ExcelInput', name: 'Excel输入', icon: 'el-icon-document' },
          { type: 'TextInput', name: '文本输入', icon: 'el-icon-document-copy' }
        ],
        '输出': [
          { type: 'TableOutput', name: '表输出', icon: 'el-icon-folder-add' },
          { type: 'ExcelOutput', name: 'Excel输出', icon: 'el-icon-document-add' },
          { type: 'TextOutput', name: '文本输出', icon: 'el-icon-document-delete' }
        ],
        '转换': [
          { type: 'FilterRows', name: '过滤记录', icon: 'el-icon-filter' },
          { type: 'SortRows', name: '排序记录', icon: 'el-icon-sort' },
          { type: 'GroupBy', name: '分组', icon: 'el-icon-data-analysis' }
        ],
        '脚本': [
          { type: 'JavaScript', name: 'JavaScript', icon: 'el-icon-edit-outline' },
          { type: 'UserDefinedJavaClass', name: 'Java类', icon: 'el-icon-edit' },
          { type: 'Formula', name: '公式', icon: 'el-icon-set-up' }
        ],
        '流程': [
          { type: 'Dummy', name: '空操作', icon: 'el-icon-minus' },
          { type: 'Abort', name: '中止', icon: 'el-icon-circle-close' }
        ]
      }
    }
  },
  methods: {
    handleDragStart(component) {
      this.dragComponent = {
        ...component,
        id: uuidv4(),
        top: 0,
        left: 0
      }
    },
    handleNodeSelect(node) {
      this.currentNode = node
    },
    async saveTrans() {
      try {
        const transData = this.$refs.flowMain.getFlowData()
        await save(transData)
        this.$message.success('保存成功')
      } catch (error) {
        this.$message.error('保存失败：' + error.message)
      }
    },
    async runTrans() {
      try {
        const transData = this.$refs.flowMain.getFlowData()
        await run(transData)
        this.showLog = true
        this.pollExecutionResult()
      } catch (error) {
        this.$message.error('运行失败：' + error.message)
      }
    },
    async stopTrans() {
      try {
        const transData = this.$refs.flowMain.getFlowData()
        await stop(transData)
        this.$message.success('已停止转换')
      } catch (error) {
        this.$message.error('停止失败：' + error.message)
      }
    },
    async verifyTrans() {
      try {
        const transData = this.$refs.flowMain.getFlowData()
        const result = await verify(transData)
        if (result.valid) {
          this.$message.success('校验通过')
        } else {
          this.$message.warning('校验不通过：' + result.message)
        }
      } catch (error) {
        this.$message.error('校验失败：' + error.message)
      }
    },
    async previewTrans() {
      try {
        const transData = this.$refs.flowMain.getFlowData()
        const result = await preview(transData)
        // TODO: 显示预览数据
      } catch (error) {
        this.$message.error('预览失败：' + error.message)
      }
    },
    async debugTrans() {
      try {
        const transData = this.$refs.flowMain.getFlowData()
        await debug(transData)
        this.showLog = true
        // TODO: 实现调试功能
      } catch (error) {
        this.$message.error('调试失败：' + error.message)
      }
    },
    async pollExecutionResult() {
      try {
        const result = await executeResult()
        this.executionLog = result.log
        if (result.status === 'RUNNING') {
          setTimeout(() => this.pollExecutionResult(), 1000)
        }
      } catch (error) {
        console.error('获取执行结果失败：', error)
      }
    },
    getStepComponent(type) {
      const componentMap = {
        TableInput: 'table-input',
        TableOutput: 'table-output',
        FilterRows: 'filter-rows',
        SortRows: 'sort-rows',
        MergeJoin: 'merge-join'
      }
      return componentMap[type] || null
    },
    updateNodeConfig(config) {
      if (this.currentNode) {
        this.currentNode.config = config
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.trans-editor {
  height: 100%;
  display: flex;
  flex-direction: column;

  .trans-header {
    height: 50px;
    padding: 8px;
    border-bottom: 1px solid #dcdfe6;
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  .trans-content {
    flex: 1;
    display: flex;
    overflow: hidden;

    .left-panel {
      width: 200px;
      border-right: 1px solid #dcdfe6;
      display: flex;
      flex-direction: column;

      .panel-title {
        height: 40px;
        line-height: 40px;
        padding: 0 16px;
        font-weight: bold;
        background-color: #f5f7fa;
        border-bottom: 1px solid #dcdfe6;
      }

      .component-list {
        flex: 1;
        overflow-y: auto;
        padding: 8px;

        .el-collapse {
          border: none;
        }

        .component-item {
          height: 32px;
          line-height: 32px;
          padding: 0 8px;
          margin-bottom: 4px;
          border: 1px dashed #dcdfe6;
          border-radius: 4px;
          cursor: move;
          user-select: none;
          display: flex;
          align-items: center;

          &:hover {
            background-color: #ecf5ff;
            border-color: #409eff;
          }

          i {
            margin-right: 8px;
            font-size: 16px;
            color: #666;
          }
        }
      }
    }

    .center-panel {
      flex: 1;
      background-color: #fff;
      position: relative;
      overflow: hidden;
    }

    .right-panel {
      width: 300px;
      border-left: 1px solid #dcdfe6;
      display: flex;
      flex-direction: column;

      .panel-title {
        height: 40px;
        line-height: 40px;
        padding: 0 16px;
        font-weight: bold;
        background-color: #f5f7fa;
        border-bottom: 1px solid #dcdfe6;
      }

      .panel-content {
        flex: 1;
        overflow-y: auto;
        padding: 16px;
      }
    }
  }

  .trans-log {
    height: 200px;
    border-top: 1px solid #dcdfe6;
    display: flex;
    flex-direction: column;

    .log-header {
      height: 40px;
      line-height: 40px;
      padding: 0 16px;
      background-color: #f5f7fa;
      border-bottom: 1px solid #dcdfe6;
      display: flex;
      justify-content: space-between;
      align-items: center;
    }

    .log-content {
      flex: 1;
      overflow-y: auto;
      padding: 8px;
      background-color: #1e1e1e;
      color: #fff;
      font-family: monospace;

      pre {
        margin: 0;
        white-space: pre-wrap;
        word-wrap: break-word;
      }
    }
  }
}
</style>
