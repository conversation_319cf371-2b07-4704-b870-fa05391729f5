<template>
  <div class="oauth2-apps-container">
    <el-row :gutter="20">
      <el-col :span="24">
        <div class="section-title">
          <i class="el-icon-connection"></i>
          统一门户
        </div>
      </el-col>
      
      <!-- 加载中状态 -->
      <el-col :span="24" v-if="isLoading">
        <div class="loading-state">
          <i class="el-icon-loading"></i>
          <p>正在加载应用...</p>
        </div>
      </el-col>
      
      <!-- 空状态 -->
      <el-col :span="24" v-else-if="!hasApps">
        <div class="empty-state">
          <i class="el-icon-info"></i>
          <p>暂无可用的集成应用</p>
        </div>
      </el-col>
      
      <!-- 应用列表 -->
      <el-col :span="24" v-if="!isLoading && hasApps">
        <div class="apps-container">
          <!-- 宽度调整控制器 -->
          <div class="width-controller">
            <div class="width-info">
              <span>卡片宽度: {{ cardWidth }}px</span>
              <span class="cards-per-row">预计每行: {{ estimatedCardsPerRow }} 个</span>
            </div>
            <el-slider
              v-model="cardWidth"
              :min="100"
              :max="200"
              :step="10"
              style="width: 200px; margin-left: 20px;"
              @change="saveCardWidth"
            />
          </div>

          <div class="apps-grid" :style="{ '--card-width': cardWidth + 'px' }">
            <div
              class="app-card"
              v-for="app in appList"
              :key="app.clientId"
              @click="handleAppClick(app)"
              :style="{ width: cardWidth + 'px', height: cardWidth + 'px' }"
            >
              <div class="app-icon">
                <img :src="app.logo || defaultLogo" :alt="app.name">
              </div>
              <div class="app-info">
                <h3>{{ app.name }}</h3>
              </div>
            </div>
          </div>
        </div>
      </el-col>
    </el-row>
  </div>
</template>

<script>
import { mapState, mapActions, mapGetters } from 'vuex'
import defaultLogo from "@/assets/icon_nopicture_filled.png"
import { authorizeApp } from '@/api/system/oauth2/oauth2Client'

export default {
  name: 'OAuth2Apps',
  data() {
    return {
      defaultLogo,
      cardWidth: parseInt(localStorage.getItem('oauth2-card-width')) || 140 // 默认卡片宽度
    }
  },
  computed: {
    ...mapState('oauth2', ['accessibleApps']),
    ...mapGetters('oauth2', ['appList', 'hasApps', 'isLoading']),
    // 估算每行能显示多少个卡片
    estimatedCardsPerRow() {
      // 假设容器宽度为1200px（可以根据实际情况调整）
      const containerWidth = 1200
      const gap = 16
      return Math.floor((containerWidth + gap) / (this.cardWidth + gap))
    }
  },
  created() {
    console.log('OAuth2Apps组件创建')
    this.loadApps()
  },
  mounted() {
    console.log('OAuth2Apps组件已挂载')
    console.log('应用列表:', this.appList)
    console.log('加载状态:', this.isLoading)
    console.log('是否有应用:', this.hasApps)
  },
  methods: {
    ...mapActions('oauth2', ['getAccessibleApps']),
    async loadApps() {
      try {
        console.log('开始加载OAuth2应用列表')
        const result = await this.getAccessibleApps()
        console.log('OAuth2应用列表加载结果:', result)
      } catch (error) {
        console.error('获取应用列表失败:', error)
        this.$message.error('获取应用列表失败：' + error.message)
      }
    },
    async handleAppClick(app) {
      console.log('点击应用：', JSON.stringify(app))
      if (!app.redirectUris || app.redirectUris.length === 0) {
        this.$message.warning('应用未配置回调地址')
        return
      }
      
      try {
        const res = await authorizeApp(app.clientId, app.redirectUris[0])
        if (res.data) {
          window.open(res.data, '_blank')
        } else {
          this.$message.warning('授权失败：未获取到重定向地址')
        }
      } catch (error) {
        console.error('授权请求失败:', error)
        this.$message.error(`授权失败: ${error.message}`)
      }
    },
    // 保存卡片宽度到本地存储
    saveCardWidth(width) {
      localStorage.setItem('oauth2-card-width', width.toString())
    }
  }
}
</script>

<style lang="scss" scoped>
.oauth2-apps-container {
  padding: 20px;

  .section-title {
    font-size: 18px;
    font-weight: bold;
    margin-bottom: 20px;
    color: #303133;
    
    i {
      margin-right: 8px;
    }
  }
  
  .loading-state,
  .empty-state {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 40px 0;
    
    i {
      font-size: 48px;
      color: #909399;
      margin-bottom: 16px;
    }
    
    p {
      font-size: 16px;
      color: #606266;
    }
  }

  .apps-container {
    width: 100%;
  }

  .width-controller {
    display: flex;
    align-items: center;
    margin-bottom: 20px;
    padding: 15px;
    background: #f8f9fa;
    border-radius: 8px;
    border: 1px solid #e9ecef;

    .width-info {
      display: flex;
      flex-direction: column;
      gap: 4px;

      span {
        font-size: 14px;
        color: #495057;
        font-weight: 500;
      }

      .cards-per-row {
        font-size: 12px;
        color: #6c757d;
        font-weight: 400;
      }
    }
  }

  .apps-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(var(--card-width, 140px), 1fr));
    gap: 16px;
    justify-content: start;
    align-items: start;
    width: 100%;
  }

  .app-card {
    // 宽度和高度通过内联样式动态设置
    padding: 12px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    background: #fff;
    border-radius: 12px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
    cursor: pointer;
    position: relative;
    overflow: hidden;

    &:hover {
      transform: translateY(-3px) scale(1.02);
      box-shadow: 0 8px 20px rgba(0, 0, 0, 0.15);
      z-index: 1;

      &::after {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 3px;
        background: #409EFF;
        animation: highlight 0.3s ease-out;
      }
    }

    &:active {
      transform: translateY(-1px) scale(0.98);
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    }

    .app-icon {
      margin-bottom: 8px;
      display: flex;
      justify-content: center;
      align-items: center;
      width: 100%;
      flex: 1;

      img {
        width: calc(var(--card-width, 140px) * 0.45);  // 图标大小根据卡片宽度动态调整
        height: calc(var(--card-width, 140px) * 0.45);
        max-width: 80px;
        max-height: 80px;
        min-width: 40px;
        min-height: 40px;
        border-radius: 12px;
        object-fit: contain;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
      }
    }

    .app-info {
      width: 100%;
      text-align: center;
      flex-shrink: 0;

      h3 {
        font-size: clamp(10px, calc(var(--card-width, 140px) * 0.08), 14px);  // 字体大小根据卡片宽度动态调整
        margin: 0;
        line-height: 1.3;
        max-height: calc(var(--card-width, 140px) * 0.25);  // 高度根据卡片宽度调整
        overflow: hidden;
        text-overflow: ellipsis;
        display: -webkit-box;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
        color: #303133;
        word-break: break-word;
      }
    }
  }

  // 响应式设计
  @media (max-width: 1200px) {
    .apps-grid {
      grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
    }

    .width-controller {
      flex-direction: column;
      align-items: flex-start;
      gap: 10px;

      .width-info {
        flex-direction: row;
        gap: 20px;
      }

      .el-slider {
        width: 100% !important;
        max-width: 300px;
      }
    }
  }

  @media (max-width: 768px) {
    .apps-grid {
      grid-template-columns: repeat(auto-fill, minmax(100px, 1fr));
      gap: 12px;
      justify-content: center;
    }

    .width-controller {
      padding: 10px;

      .width-info {
        flex-direction: column;
        gap: 4px;

        span {
          font-size: 12px;
        }
      }
    }
  }

  @media (max-width: 480px) {
    .apps-grid {
      grid-template-columns: repeat(auto-fill, minmax(80px, 1fr));
      gap: 8px;
    }

    .width-controller {
      display: none; // 在小屏幕上隐藏宽度控制器
    }
  }
}

@keyframes highlight {
  from {
    transform: scaleX(0);
    opacity: 0;
  }
  to {
    transform: scaleX(1);
    opacity: 1;
  }
}
</style>
