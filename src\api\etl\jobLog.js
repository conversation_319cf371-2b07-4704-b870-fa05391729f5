import request from '@/utils/request'

// 获取ETL作业日志详情
export function getJobLog(id) {
  return request({
    url: '/etl/job-log/get',
    method: 'get',
    params: { id }
  })
}

// 获取ETL作业的最近日志列表
export function getRecentJobLogList(jobId, limit) {
  return request({
    url: '/etl/job-log/list-by-job-id',
    method: 'get',
    params: { jobId, limit }
  })
}

// 获取ETL作业日志分页
export function getJobLogPage(query) {
  return request({
    url: '/etl/job-log/page',
    method: 'get',
    params: query
  })
} 