<template>
  <div class="table-input-config">
    <el-form label-position="top" size="small">
      <el-form-item label="数据库连接">
        <el-select v-model="config.connection" placeholder="请选择数据库连接">
          <el-option
            v-for="conn in dbConnections"
            :key="conn.id"
            :label="conn.name"
            :value="conn.id"
          />
        </el-select>
      </el-form-item>
      
      <el-form-item label="SQL查询">
        <el-input
          type="textarea"
          v-model="config.sql"
          :rows="8"
          placeholder="请输入SQL语句"
        />
      </el-form-item>

      <el-form-item>
        <el-checkbox v-model="config.executeForEachRow">为每一行执行</el-checkbox>
      </el-form-item>

      <el-form-item label="变量替换">
        <el-checkbox v-model="config.variableReplace">启用变量替换</el-checkbox>
      </el-form-item>

      <el-form-item label="延迟字段定义">
        <el-checkbox v-model="config.lazyConversion">启用延迟转换</el-checkbox>
      </el-form-item>

      <template v-if="config.executeForEachRow">
        <el-divider content-position="left">参数设置</el-divider>
        <div class="params-list">
          <div v-for="(param, index) in config.parameters" :key="index" class="param-item">
            <el-input v-model="config.parameters[index]" placeholder="参数名称">
              <template slot="append">
                <el-button @click="removeParameter(index)" icon="el-icon-delete"></el-button>
              </template>
            </el-input>
          </div>
          <el-button type="text" @click="addParameter" icon="el-icon-plus">添加参数</el-button>
        </div>
      </template>
    </el-form>
    
    <div class="buttons">
      <el-button type="primary" size="small" @click="previewData">预览数据</el-button>
      <el-button size="small" @click="getFields">获取字段</el-button>
    </div>

    <!-- 预览数据对话框 -->
    <el-dialog title="数据预览" :visible.sync="previewVisible" width="80%">
      <el-table :data="previewData" border stripe height="400">
        <el-table-column
          v-for="field in previewFields"
          :key="field.name"
          :prop="field.name"
          :label="field.name"
          :width="field.width"
        />
      </el-table>
    </el-dialog>
  </div>
</template>

<script>
export default {
  name: 'TableInput',
  props: {
    value: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      config: {
        connection: '',
        sql: '',
        executeForEachRow: false,
        variableReplace: true,
        lazyConversion: false,
        parameters: []
      },
      dbConnections: [],
      previewVisible: false,
      previewData: [],
      previewFields: []
    }
  },
  watch: {
    value: {
      handler(val) {
        this.config = { ...this.config, ...val }
      },
      immediate: true,
      deep: true
    },
    config: {
      handler(val) {
        this.$emit('input', val)
      },
      deep: true
    }
  },
  methods: {
    addParameter() {
      this.config.parameters.push('')
    },
    removeParameter(index) {
      this.config.parameters.splice(index, 1)
    },
    async previewData() {
      try {
        // TODO: 调用预览数据接口
        this.previewVisible = true
      } catch (error) {
        this.$message.error('获取预览数据失败：' + error.message)
      }
    },
    async getFields() {
      try {
        // TODO: 调用获取字段接口
        this.$message.success('获取字段成功')
      } catch (error) {
        this.$message.error('获取字段失败：' + error.message)
      }
    }
  },
  async created() {
    try {
      // TODO: 获取数据库连接列表
    } catch (error) {
      console.error('获取数据库连接失败：', error)
    }
  }
}
</script>

<style lang="scss" scoped>
.table-input-config {
  padding: 16px;

  .params-list {
    .param-item {
      margin-bottom: 8px;
    }
  }

  .buttons {
    margin-top: 16px;
    display: flex;
    justify-content: flex-end;
    gap: 8px;
  }
}
</style>
