const keywords = [
  "break", "case", "catch", "class", "const", "continue", "debugger",
  "default", "delete", "do", "else", "export", "extends", "finally",
  "for", "function", "if", "import", "in", "instanceof", "new", "return",
  "super", "switch", "this", "throw", "try", "typeof", "var", "void",
  "while", "with", "yield", "async", "await", "let"
];

const builtInObjects = [
  "Array", "Boolean", "Date", "Error", "Function", "JSON", "Math",
  "Number", "Object", "RegExp", "String", "Promise", "Map", "Set",
  "Symbol", "console", "window", "document"
];

const methods = [
  "forEach", "map", "filter", "reduce", "find", "findIndex", "includes",
  "indexOf", "join", "slice", "splice", "push", "pop", "shift", "unshift",
  "concat", "sort", "reverse", "toString", "valueOf"
];

export default function createJavascriptCompleter(getHints) {
  return {
    triggerCharacters: ["."],
    provideCompletionItems: function(model, position) {
      const textUntilPosition = model.getValueInRange({
        startLineNumber: position.lineNumber,
        startColumn: 1,
        endLineNumber: position.lineNumber,
        endColumn: position.column
      });

      const suggestions = [];

      // 添加关键字建议
      keywords.forEach(keyword => {
        suggestions.push({
          label: keyword,
          kind: monaco.languages.CompletionItemKind.Keyword,
          insertText: keyword,
          detail: "JavaScript关键字"
        });
      });

      // 添加内置对象建议
      builtInObjects.forEach(obj => {
        suggestions.push({
          label: obj,
          kind: monaco.languages.CompletionItemKind.Class,
          insertText: obj,
          detail: "JavaScript内置对象"
        });
      });

      // 添加方法建议
      methods.forEach(method => {
        suggestions.push({
          label: method,
          kind: monaco.languages.CompletionItemKind.Method,
          insertText: method,
          detail: "JavaScript方法"
        });
      });

      // 添加自定义提示
      const hints = getHints(model);
      hints.forEach(hint => {
        suggestions.push({
          label: hint.label || hint,
          kind: hint.kind || monaco.languages.CompletionItemKind.Text,
          insertText: hint.insertText || hint.label || hint,
          detail: hint.detail || "自定义提示"
        });
      });

      return {
        suggestions: suggestions
      };
    }
  };
} 