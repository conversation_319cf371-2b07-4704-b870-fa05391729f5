import request from '@/utils/request'

// 查询模板列表
export function listTemplate(query) {
  return request({
    url: '/report/template/list',
    method: 'get',
    params: query
  })
}

// 查询模板详细
export function getTemplate(id) {
  return request({
    url: '/report/template/' + id,
    method: 'get'
  })
}

// 新增模板
export function addTemplate(data) {
  return request({
    url: '/report/template',
    method: 'post',
    data: data
  })
}

// 修改模板
export function updateTemplate(data) {
  return request({
    url: '/report/template',
    method: 'put',
    data: data
  })
}

// 删除模板
export function deleteTemplate(id) {
  return request({
    url: '/report/template/' + id,
    method: 'delete'
  })
}

// 批量删除模板
export function deleteTemplateBatch(ids) {
  return request({
    url: '/report/template/batch/' + ids,
    method: 'delete'
  })
}

// 更新模板状态
export function updateTemplateStatus(id, status) {
  return request({
    url: '/report/template/status',
    method: 'put',
    data: {
      id,
      status
    }
  })
}

// 预览模板
export function previewTemplate(data) {
  return request({
    url: '/report/template/preview',
    method: 'post',
    data: data
  })
}

// 导出模板
export function exportTemplate(data) {
  return request({
    url: '/report/template/export',
    method: 'post',
    data: data,
    responseType: 'blob'
  })
}

// 下载模板示例
export function downloadTemplateExample() {
  return request({
    url: '/report/template/example',
    method: 'get',
    responseType: 'blob'
  })
} 