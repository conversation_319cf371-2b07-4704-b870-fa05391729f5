/**
 * 文件大小格式化
 * @param {number} size 文件大小
 * @returns {string} 格式化后的文件大小
 */
export function formatFileSize(size) {
  if (size === 0) return '0 B'
  const k = 1024
  const sizes = ['B', 'KB', 'MB', 'GB', 'TB', 'PB', 'EB', 'ZB', 'YB']
  const i = Math.floor(Math.log(size) / Math.log(k))
  return (size / Math.pow(k, i)).toPrecision(3) + ' ' + sizes[i]
}

/**
 * 获取文件扩展名
 * @param {string} filename 文件名
 * @returns {string} 文件扩展名
 */
export function getFileExt(filename) {
  return filename.substring(filename.lastIndexOf('.') + 1)
}

/**
 * 获取文件名（不含扩展名）
 * @param {string} filename 文件名
 * @returns {string} 文件名（不含扩展名）
 */
export function getFileName(filename) {
  return filename.substring(0, filename.lastIndexOf('.'))
}

/**
 * 判断文件类型是否为图片
 * @param {string} filename 文件名
 * @returns {boolean} 是否为图片
 */
export function isImage(filename) {
  const ext = getFileExt(filename).toLowerCase()
  return ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp'].includes(ext)
}

/**
 * 判断文件类型是否为Excel
 * @param {string} filename 文件名
 * @returns {boolean} 是否为Excel文件
 */
export function isExcel(filename) {
  const ext = getFileExt(filename).toLowerCase()
  return ['xls', 'xlsx'].includes(ext)
}

/**
 * 下载文件
 * @param {Blob} data 文件数据
 * @param {string} filename 文件名
 */
export function downloadFile(data, filename) {
  const blob = new Blob([data])
  const link = document.createElement('a')
  link.href = window.URL.createObjectURL(blob)
  link.download = filename
  document.body.appendChild(link)
  link.click()
  window.URL.revokeObjectURL(link.href)
  document.body.removeChild(link)
}

/**
 * 读取本地文件
 * @param {File} file 文件对象
 * @returns {Promise} Promise对象
 */
export function readFile(file) {
  return new Promise((resolve, reject) => {
    const reader = new FileReader()
    reader.onload = e => resolve(e.target.result)
    reader.onerror = e => reject(e)
    reader.readAsText(file)
  })
}

/**
 * 读取本地图片并转换为Base64
 * @param {File} file 图片文件
 * @returns {Promise} Promise对象
 */
export function readImageAsBase64(file) {
  return new Promise((resolve, reject) => {
    const reader = new FileReader()
    reader.onload = e => resolve(e.target.result)
    reader.onerror = e => reject(e)
    reader.readAsDataURL(file)
  })
}

/**
 * 压缩图片
 * @param {string} base64 图片base64数据
 * @param {number} quality 压缩质量，0-1之间
 * @returns {Promise} Promise对象
 */
export function compressImage(base64, quality = 0.8) {
  return new Promise((resolve, reject) => {
    const img = new Image()
    img.src = base64
    img.onload = () => {
      const canvas = document.createElement('canvas')
      const ctx = canvas.getContext('2d')
      canvas.width = img.width
      canvas.height = img.height
      ctx.drawImage(img, 0, 0, canvas.width, canvas.height)
      resolve(canvas.toDataURL('image/jpeg', quality))
    }
    img.onerror = e => reject(e)
  })
} 