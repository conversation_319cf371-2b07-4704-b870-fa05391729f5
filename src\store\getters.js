const getters = {
  sidebar: state => state.app.sidebar,
  size: state => state.app.size,
  device: state => state.app.device,
  visitedViews: state => state.tagsView.visitedViews,
  cachedViews: state => state.tagsView.cachedViews,
  token: state => state.user.token,
  avatar: state => state.user.avatar,
  name: state => state.user.name,
  nickname: state => state.user.nickname,
  introduction: state => state.user.introduction,
  roles: state => state.user.roles,
  permissions: state => state.user.permissions,
  permission_routes: state => state.permission.routes,
 // 工具栏
 topbarRouters:state => state.permission.topbarRouters,
 defaultRoutes:state => state.permission.defaultRoutes,
 sidebarRouters:state => state.permission.sidebarRouters,
 // 数据字典
 dict_datas: state => state.dict.dictDatas,
  // 报表相关
  currentReport: state => state.report.currentReport,
  categoryTree: state => state.report.categoryTree,
  reportList: state => state.report.reportList,
  reportPermissions: state => state.report.reportPermissions,
  exportLogs: state => state.report.exportLogs,
  accessLogs: state => state.report.accessLogs,
  reportStatistics: state => state.report.statistics,
  // 日志相关
  logAccessLogs: state => state.log.accessLogs,
  logExportLogs: state => state.log.exportLogs,
  logStatistics: state => state.log.statistics,
  logQueryParams: state => state.log.queryParams,
  logTrendData: state => state.log.statistics.trend,
  logTopReportsData: state => state.log.statistics.topReports,
  logAreaData: state => state.log.statistics.areaDistribution,
  logDeviceData: state => state.log.statistics.deviceDistribution,
  // 设计器相关
  currentScreen: state => state.designer.currentScreen,
  selectedComponent: state => state.designer.selectedComponent,
  designerComponents: state => state.designer.components,
  canvas: state => state.designer.canvas,
  canUndo: state => state.designer.canUndo,
  canRedo: state => state.designer.canRedo,
  isPreview: state => state.designer.isPreview,
  userId: state => state.user.id
}

export default getters
