import request from '@/utils/request';

// 查看作业
export function explore(params) {
  return request({
    url: '/etl/job/explore',
    method: 'get',
    params
  });
}

// 暂停作业
export function pause(params) {
  return request({
    url: '/etl/job/pause',
    method: 'post',
    params
  });
}

// 重放作业
export function replay(params) {
  return request({
    url: '/etl/job/replay',
    method: 'post',
    params
  });
}

// 获取作业执行日志、状态及结果
export function executeResult(params) {
  return request({
    url: '/etl/job/result',
    method: 'get',
    params
  });
}

// 保存作业
export function save(params) {
  return request({
    url: '/etl/job/save',
    method: 'post',
    data: params
  });
}

// 执行作业
export function run(params) {
  return request({
    url: '/etl/job/run',
    method: 'post',
    data: params
  });
}

// 停止作业
export function stop(params) {
  return request({
    url: '/etl/job/stop',
    method: 'post',
    params
  });
}

// 获取作业文件
export function kjb(params) {
  return request({
    url: '/etl/job/kjb',
    method: 'post',
    data: params
  });
}

// 测试
export function test(params) {
  return request({
    url: '/etl/job/test',
    method: 'post',
    params
  });
}

// 创建ETL作业
export function createEtlJob(data) {
  return request({
    url: '/etl/job/create',
    method: 'post',
    data: data
  });
}

// 更新ETL作业
export function updateEtlJob(data) {
  return request({
    url: '/etl/job/update',
    method: 'put',
    data: data
  });
}

// 删除ETL作业
export function deleteEtlJob(id) {
  return request({
    url: '/etl/job/delete',
    method: 'delete',
    params: { id }
  });
}

// 获取ETL作业详情
export function getEtlJob(id) {
  return request({
    url: '/etl/job/get',
    method: 'get',
    params: { id }
  });
}

// 获取ETL作业列表
export function getEtlJobList(ids) {
  return request({
    url: '/etl/job/list',
    method: 'get',
    params: { ids }
  });
}

// 获取ETL作业分页
export function getEtlJobPage(query) {
  return request({
    url: '/etl/job/page',
    method: 'get',
    params: query
  });
}

// 执行ETL作业
export function executeEtlJob(id) {
  return request({
    url: '/etl/job/execute',
    method: 'post',
    params: { id }
  });
}

// 停止ETL作业
export function stopEtlJob(id) {
  return request({
    url: '/etl/job/stop',
    method: 'post',
    params: { id }
  });
}
