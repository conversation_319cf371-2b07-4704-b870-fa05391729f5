<template>
  <div class="rest-config">
    <el-form label-width="100px">
      <el-form-item label="路径">
        <el-input v-model="config.path" placeholder="/api/example" />
      </el-form-item>
      <el-form-item label="方法">
        <el-select v-model="config.method">
          <el-option label="GET" value="get" />
          <el-option label="POST" value="post" />
          <el-option label="PUT" value="put" />
          <el-option label="DELETE" value="delete" />
        </el-select>
      </el-form-item>
      <el-form-item label="请求头">
        <div v-for="(header, index) in config.headers" :key="index" class="header-item">
          <el-input v-model="header.key" placeholder="Key" style="width: 200px;" />
          <el-input v-model="header.value" placeholder="Value" style="width: 200px; margin: 0 10px;" />
          <el-button type="text" @click="removeHeader(index)">删除</el-button>
        </div>
        <el-button type="text" @click="addHeader">添加请求头</el-button>
      </el-form-item>
      <el-form-item label="数据格式">
        <el-select v-model="config.dataFormat">
          <el-option label="JSON" value="json" />
          <el-option label="XML" value="xml" />
          <el-option label="Form" value="form" />
        </el-select>
      </el-form-item>
    </el-form>
  </div>
</template>

<script>
export default {
  name: 'RestConfig',
  props: {
    value: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      config: {
        path: '',
        method: 'get',
        headers: [],
        dataFormat: 'json'
      }
    }
  },
  watch: {
    value: {
      handler(val) {
        this.config = { ...this.config, ...val }
      },
      immediate: true
    },
    config: {
      handler(val) {
        this.$emit('input', val)
      },
      deep: true
    }
  },
  methods: {
    addHeader() {
      this.config.headers.push({ key: '', value: '' })
    },
    removeHeader(index) {
      this.config.headers.splice(index, 1)
    }
  }
}
</script>

<style scoped>
.header-item {
  display: flex;
  align-items: center;
  margin-bottom: 10px;
}
</style> 