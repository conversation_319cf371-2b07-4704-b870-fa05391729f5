import request from '@/utils/request'

// 仪表盘相关接口
export function listDashboard(query) {
  return request({
    url: '/report/bi/dashboard/list',
    method: 'get',
    params: query
  })
}

export function getDashboard(id) {
  return request({
    url: `/report/bi/dashboard/${id}`,
    method: 'get'
  })
}

export function createDashboard(data) {
  return request({
    url: '/report/bi/dashboard',
    method: 'post',
    data: data
  })
}

export function updateDashboard(data) {
  return request({
    url: '/report/bi/dashboard',
    method: 'put',
    data: data
  })
}

export function deleteDashboard(id) {
  return request({
    url: `/report/bi/dashboard/${id}`,
    method: 'delete'
  })
}

// 数据源相关接口
export function listDataSource(query) {
  return request({
    url: '/report/bi/datasource/list',
    method: 'get',
    params: query
  })
}

export function getDataSource(id) {
  return request({
    url: `/report/bi/datasource/${id}`,
    method: 'get'
  })
}

export function createDataSource(data) {
  return request({
    url: '/report/bi/datasource',
    method: 'post',
    data: data
  })
}

export function updateDataSource(data) {
  return request({
    url: '/report/bi/datasource',
    method: 'put',
    data: data
  })
}

export function deleteDataSource(id) {
  return request({
    url: `/report/bi/datasource/${id}`,
    method: 'delete'
  })
}

export function testDataSource(data) {
  return request({
    url: '/report/bi/datasource/test',
    method: 'post',
    data: data
  })
}

// 数据集相关接口
export function listDataset(query) {
  return request({
    url: '/report/bi/dataset/list',
    method: 'get',
    params: query
  })
}

export function getDataset(id) {
  return request({
    url: `/report/bi/dataset/${id}`,
    method: 'get'
  })
}

export function createDataset(data) {
  return request({
    url: '/report/bi/dataset',
    method: 'post',
    data: data
  })
}

export function updateDataset(data) {
  return request({
    url: '/report/bi/dataset',
    method: 'put',
    data: data
  })
}

export function deleteDataset(id) {
  return request({
    url: `/report/bi/dataset/${id}`,
    method: 'delete'
  })
}

export function previewDataset(data) {
  return request({
    url: '/report/bi/dataset/preview',
    method: 'post',
    data: data
  })
}

// 图表相关接口
export function listChart(query) {
  return request({
    url: '/report/bi/chart/list',
    method: 'get',
    params: query
  })
}

export function getChart(id) {
  return request({
    url: `/report/bi/chart/${id}`,
    method: 'get'
  })
}

export function createChart(data) {
  return request({
    url: '/report/bi/chart',
    method: 'post',
    data: data
  })
}

export function updateChart(data) {
  return request({
    url: '/report/bi/chart',
    method: 'put',
    data: data
  })
}

export function deleteChart(id) {
  return request({
    url: `/report/bi/chart/${id}`,
    method: 'delete'
  })
}

// 数据查询接口
export function queryChartData(id, params) {
  return request({
    url: `/report/bi/chart/${id}/data`,
    method: 'get',
    params: params
  })
}

export function previewChartData(data) {
  return request({
    url: '/report/bi/chart/preview',
    method: 'post',
    data: data
  })
}

// 仪表盘导入导出
export function exportDashboard(id) {
  return request({
    url: `/report/bi/dashboard/${id}/export`,
    method: 'get',
    responseType: 'blob'
  })
}

export function importDashboard(data) {
  return request({
    url: '/report/bi/dashboard/import',
    method: 'post',
    headers: {
      'Content-Type': 'multipart/form-data'
    },
    data: data
  })
}

// 仪表盘模板
export function listTemplate() {
  return request({
    url: '/report/bi/template/list',
    method: 'get'
  })
}

export function getTemplate(id) {
  return request({
    url: `/report/bi/template/${id}`,
    method: 'get'
  })
}

export function saveAsTemplate(data) {
  return request({
    url: '/report/bi/template',
    method: 'post',
    data: data
  })
}

export function deleteTemplate(id) {
  return request({
    url: `/report/bi/template/${id}`,
    method: 'delete'
  })
}

export function applyTemplate(dashboardId, templateId) {
  return request({
    url: `/report/bi/dashboard/${dashboardId}/apply-template/${templateId}`,
    method: 'post'
  })
} 