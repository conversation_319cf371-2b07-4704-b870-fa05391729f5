import request from '@/utils/request'

// 获取字段
export function getColumn(id) {
  return request({
    url: `/metadata/column/${id}`,
    method: 'get'
  })
}

// 获取字段列表(通过表ID)
export function getColumnListByTableId(tableId) {
  return request({
    url: '/metadata/column/list-by-table-id',
    method: 'get',
    params: { tableId }
  })
}

// 获取字段列表(通过数据源ID)
export function getColumnListBySourceId(sourceId) {
  return request({
    url: '/metadata/column/list-by-source-id',
    method: 'get',
    params: { sourceId }
  })
}

// 获取字段分页
export function getColumnPage(query) {
  return request({
    url: '/metadata/column/page',
    method: 'get',
    params: query
  })
}

// 获取表的字段树
export function getColumnTree(tableId) {
  return request({
    url: `/metadata/column/tree/table`,
    method: 'get'
  })
} 