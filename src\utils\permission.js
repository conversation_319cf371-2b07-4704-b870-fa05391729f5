import store from '@/store'

/**
 * 检查用户是否有指定权限
 * @param {string|array} permission 权限标识或权限标识数组
 * @returns {boolean} 是否有权限
 */
export function hasPermission(permission) {
  const all_permission = '*:*:*'
  const permissions = store.getters.permissions
  if (!permission || !permissions) return false
  if (permissions.includes(all_permission)) return true
  if (Array.isArray(permission)) {
    return permission.some(p => permissions.includes(p))
  }
  return permissions.includes(permission)
}

/**
 * 检查用户是否有指定角色
 * @param {string|array} role 角色标识或角色标识数组
 * @returns {boolean} 是否有角色
 */
export function hasRole(role) {
  const super_admin = 'admin'
  const roles = store.getters.roles
  if (!role || !roles) return false
  if (roles.includes(super_admin)) return true
  if (Array.isArray(role)) {
    return role.some(r => roles.includes(r))
  }
  return roles.includes(role)
}

/**
 * 检查用户是否有指定数据权限
 * @param {string} dataScope 数据权限标识
 * @returns {boolean} 是否有数据权限
 */
export function hasDataScope(dataScope) {
  const roles = store.getters.roles
  if (!dataScope || !roles) return false
  // 超级管理员拥有所有权限
  if (roles.includes('admin')) return true
  // 检查数据权限
  const dataScopeTypes = {
    ALL: '1', // 全部数据权限
    CUSTOM: '2', // 自定数据权限
    DEPT: '3', // 本部门数据权限
    DEPT_AND_CHILD: '4', // 本部门及以下数据权限
    SELF: '5' // 仅本人数据权限
  }
  const userDataScope = store.getters.userInfo?.dataScope || SELF
  return roles.some(role => {
    const roleDataScope = role.dataScope || SELF
    // 如果角色数据权限是ALL，或者与传入的数据权限匹配
    if (roleDataScope === dataScopeTypes.ALL) return true
    if (roleDataScope === dataScope) return true
    // 如果角色数据权限是部门及以下，且传入的是部门或个人
    if (roleDataScope === dataScopeTypes.DEPT_AND_CHILD && 
        [dataScopeTypes.DEPT, dataScopeTypes.SELF].includes(dataScope)) return true
    // 如果角色数据权限是部门，且传入的是个人
    if (roleDataScope === dataScopeTypes.DEPT && dataScope === dataScopeTypes.SELF) return true
    return false
  })
}

/**
 * 检查用户是否有指定报表权限
 * @param {string} reportId 报表ID
 * @param {string} action 操作类型（view/edit/delete/export/share）
 * @returns {boolean} 是否有权限
 */
export function hasReportPermission(reportId, action) {
  // 参数校验
  if (!reportId || !action) return false
  
  // 超级管理员拥有所有权限
  if (hasRole('admin')) return true
  
  // 检查是否有全局报表权限
  const reportPermMap = {
    view: 'report:view',
    edit: 'report:edit',
    delete: 'report:delete',
    export: 'report:export',
    share: 'report:share'
  }
  
  // 检查action是否合法
  if (!reportPermMap[action]) return false
  
  if (hasPermission(reportPermMap[action])) return true

  // 检查具体报表权限
  const permissions = store.getters.reportPermissions
  if (!permissions) return false
  
  const reportPermission = permissions.find(p => p.reportId === reportId)
  if (!reportPermission) return false
  
  // 检查数据权限
  const report = store.getters.reportList?.find(r => r.id === reportId)
  if (!report) return false
  
  // 如果是创建者，拥有全部权限
  if (report.createBy === store.getters.userId) return true
  
  // 检查操作权限
  return reportPermission.actions.includes(action)
}

/**
 * 检查用户是否有指定分类的报表权限
 * @param {string} categoryId 分类ID
 * @param {string} action 操作类型（view/edit/delete/export/share）
 * @returns {boolean} 是否有权限
 */
export function hasReportCategoryPermission(categoryId, action) {
  // 参数校验
  if (!categoryId || !action) return false
  
  // 超级管理员拥有所有权限
  if (hasRole('admin')) return true
  
  // 检查是否有全局分类权限
  const categoryPermMap = {
    view: 'report:category:view',
    edit: 'report:category:edit',
    delete: 'report:category:delete',
    export: 'report:category:export',
    share: 'report:category:share'
  }
  
  // 检查action是否合法
  if (!categoryPermMap[action]) return false
  
  if (hasPermission(categoryPermMap[action])) return true

  // 检查具体分类权限
  const permissions = store.getters.reportPermissions
  if (!permissions) return false
  
  // 检查数据权限
  const category = store.getters.categoryTree?.find(c => c.id === categoryId)
  if (!category) return false
  
  // 如果是创建者，拥有全部权限
  if (category.createBy === store.getters.userId) return true
  
  return permissions.some(p => p.categoryId === categoryId && p.actions.includes(action))
}

/**
 * 检查用户是否有指定菜单权限
 * @param {string} menuId 菜单ID
 * @returns {boolean} 是否有权限
 */
export function hasMenuPermission(menuId) {
  if (!menuId) return false
  if (hasRole('admin')) return true
  const permissions = store.getters.permissions
  if (!permissions) return false
  return permissions.some(p => p.startsWith(`menu:${menuId}`))
}

/**
 * 检查用户是否有指定按钮权限
 * @param {string} buttonId 按钮ID
 * @returns {boolean} 是否有权限
 */
export function hasButtonPermission(buttonId) {
  if (!buttonId) return false
  if (hasRole('admin')) return true
  const permissions = store.getters.permissions
  if (!permissions) return false
  return permissions.some(p => p.startsWith(`button:${buttonId}`))
}

/**
 * 检查用户是否有指定API权限
 * @param {string} api API路径
 * @param {string} method 请求方法
 * @returns {boolean} 是否有权限
 */
export function hasApiPermission(api, method) {
  if (!api || !method) return false
  if (hasRole('admin')) return true
  const permissions = store.getters.permissions
  if (!permissions) return false
  return permissions.some(p => p.startsWith(`api:${method.toLowerCase()}:${api}`))
}
