<template>
  <div class="flow-node"
    :style="nodeStyle"
    :class="{ selected: selected }"
    @mousedown="handleMouseDown"
    @click.stop="$emit('select', node)">
    <!-- 节点主体 -->
    <div class="node-content">
      <i :class="node.icon" class="node-icon"></i>
      <div class="node-title">{{ node.name }}</div>
    </div>
    <!-- 连接点 -->
    <div class="connector input-connector"
      @mouseup="handleConnectorMouseUp">
      <div class="connector-dot"></div>
    </div>
    <div class="connector output-connector"
      @mousedown.stop="handleConnectorMouseDown">
      <div class="connector-dot"></div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'FlowNode',
  props: {
    node: {
      type: Object,
      required: true
    },
    selected: {
      type: Boolean,
      default: false
    }
  },
  computed: {
    nodeStyle() {
      return {
        left: this.node.left + 'px',
        top: this.node.top + 'px'
      }
    }
  },
  methods: {
    handleMouseDown(event) {
      if (event.button !== 0) return // 只处理左键

      const startX = event.clientX
      const startY = event.clientY
      const startLeft = this.node.left
      const startTop = this.node.top

      const moveHandler = (e) => {
        const deltaX = e.clientX - startX
        const deltaY = e.clientY - startY
        this.$emit('update:position', {
          id: this.node.id,
          left: startLeft + deltaX,
          top: startTop + deltaY
        })
      }

      const upHandler = () => {
        document.removeEventListener('mousemove', moveHandler)
        document.removeEventListener('mouseup', upHandler)
      }

      document.addEventListener('mousemove', moveHandler)
      document.addEventListener('mouseup', upHandler)
    },
    handleConnectorMouseDown(event) {
      const rect = this.$el.getBoundingClientRect()
      this.$emit('connect-start', {
        node: this.node,
        x: rect.right,
        y: rect.top + rect.height / 2
      })
    },
    handleConnectorMouseUp() {
      this.$emit('connect-end', this.node)
    }
  }
}
</script>

<style lang="scss" scoped>
.flow-node {
  position: absolute;
  width: 100px;
  height: 80px;
  background-color: #fff;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.12);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  cursor: move;
  user-select: none;

  &.selected {
    border-color: #409eff;
    box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.2);
  }

  .node-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 8px;

    .node-icon {
      font-size: 24px;
      color: #666;
      margin-bottom: 4px;
    }

    .node-title {
      font-size: 12px;
      color: #333;
      text-align: center;
      width: 100%;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
  }

  .connector {
    position: absolute;
    width: 16px;
    height: 16px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;

    .connector-dot {
      width: 8px;
      height: 8px;
      background-color: #666;
      border-radius: 50%;
      transition: all 0.3s;

      &:hover {
        background-color: #409eff;
        transform: scale(1.2);
      }
    }

    &.input-connector {
      left: -8px;
      top: 50%;
      transform: translateY(-50%);
    }

    &.output-connector {
      right: -8px;
      top: 50%;
      transform: translateY(-50%);
    }
  }

  &:hover {
    .connector-dot {
      background-color: #409eff;
    }
  }
}
</style>
