const state = {
  // 当前选中的报表
  currentReport: null,
  // 报表分类树
  categoryTree: [],
  // 报表列表
  reportList: [],
  // 报表权限
  reportPermissions: [],
  // 导出记录
  exportLogs: [],
  // 访问记录
  accessLogs: [],
  // 统计数据
  statistics: {
    userCount: 0,
    reportCount: 0,
    accessCount: 0,
    avgDuration: 0
  }
}

const mutations = {
  SET_CURRENT_REPORT: (state, report) => {
    state.currentReport = report
  },
  SET_CATEGORY_TREE: (state, tree) => {
    state.categoryTree = tree
  },
  SET_REPORT_LIST: (state, list) => {
    state.reportList = list
  },
  SET_REPORT_PERMISSIONS: (state, permissions) => {
    state.reportPermissions = permissions
  },
  SET_EXPORT_LOGS: (state, logs) => {
    state.exportLogs = logs
  },
  SET_ACCESS_LOGS: (state, logs) => {
    state.accessLogs = logs
  },
  SET_STATISTICS: (state, statistics) => {
    state.statistics = statistics
  }
}

const actions = {
  // 设置当前报表
  setCurrentReport({ commit }, report) {
    commit('SET_CURRENT_REPORT', report)
  },
  // 设置分类树
  setCategoryTree({ commit }, tree) {
    commit('SET_CATEGORY_TREE', tree)
  },
  // 设置报表列表
  setReportList({ commit }, list) {
    commit('SET_REPORT_LIST', list)
  },
  // 设置报表权限
  setReportPermissions({ commit }, permissions) {
    commit('SET_REPORT_PERMISSIONS', permissions)
  },
  // 设置导出记录
  setExportLogs({ commit }, logs) {
    commit('SET_EXPORT_LOGS', logs)
  },
  // 设置访问记录
  setAccessLogs({ commit }, logs) {
    commit('SET_ACCESS_LOGS', logs)
  },
  // 设置统计数据
  setStatistics({ commit }, statistics) {
    commit('SET_STATISTICS', statistics)
  }
}

const getters = {
  currentReport: state => state.currentReport,
  categoryTree: state => state.categoryTree,
  reportList: state => state.reportList,
  reportPermissions: state => state.reportPermissions,
  exportLogs: state => state.exportLogs,
  accessLogs: state => state.accessLogs,
  statistics: state => state.statistics
}

export default {
  namespaced: true,
  state,
  mutations,
  actions,
  getters
} 