import request from '@/utils/request'

// 获取数据血缘关系列表
export function getBloodList() {
  return request({
    url: '/metadata/blood/list',
    method: 'get'
  })
}

// 获取数据血缘关系列表(通过数据源ID)
export function getBloodListBySourceId(sourceId) {
  return request({
    url: '/metadata/blood/list-by-source-id',
    method: 'get',
    params: { sourceId }
  })
}

// 获取数据血缘关系列表(通过表ID)
export function getBloodListByTableId(tableId) {
  return request({
    url: '/metadata/blood/list-by-table-id',
    method: 'get',
    params: { tableId }
  })
}

// 获取数据血缘关系列表(通过字段ID)
export function getBloodListByColumnId(columnId) {
  return request({
    url: '/metadata/blood/list-by-column-id',
    method: 'get',
    params: { columnId }
  })
} 