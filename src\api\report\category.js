import request from '@/utils/request'

// 查询分类列表
export function listCategory(query) {
  return request({
    url: '/report/category/list',
    method: 'get',
    params: query
  })
}

// 查询分类详细
export function getCategory(id) {
  return request({
    url: '/report/category/' + id,
    method: 'get'
  })
}

// 新增分类
export function addCategory(data) {
  return request({
    url: '/report/category',
    method: 'post',
    data: data
  })
}

// 修改分类
export function updateCategory(data) {
  return request({
    url: '/report/category',
    method: 'put',
    data: data
  })
}

// 删除分类
export function deleteCategory(id) {
  return request({
    url: '/report/category/' + id,
    method: 'delete'
  })
}

// 导出分类
export function exportCategory(query) {
  return request({
    url: '/report/category/export',
    method: 'get',
    params: query
  })
}

// 获取分类下拉树列表
export function treeselect() {
  return request({
    url: '/report/category/treeselect',
    method: 'get'
  })
} 