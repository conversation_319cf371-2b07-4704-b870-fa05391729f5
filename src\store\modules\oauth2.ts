import { defineStore } from 'pinia'
import { getOAuth2AppsApi, getUserAccessibleAppsApi } from '@/api/oauth2'
import type { OAuth2AppVO } from '@/api/oauth2'

interface OAuth2State {
  appList: OAuth2AppVO[]
  accessibleApps: OAuth2AppVO[]
  isLoading: boolean
}

export const useOAuth2Store = defineStore('oauth2', {
  state: (): OAuth2State => ({
    appList: [],
    accessibleApps: [],
    isLoading: false
  }),

  getters: {
    getAppList: (state) => state.appList,
    getAccessibleApps: (state) => state.accessibleApps,
    getIsLoading: (state) => state.isLoading
  },

  actions: {
    async loadAppList() {
      try {
        this.isLoading = true
        const response = await getOAuth2AppsApi()
        this.appList = response.data || []
      } catch (error) {
        console.error('加载OAuth2应用列表失败:', error)
      } finally {
        this.isLoading = false
      }
    },

    async loadAccessibleApps() {
      try {
        this.isLoading = true
        const response = await getUserAccessibleAppsApi()
        this.accessibleApps = response.data || []
      } catch (error) {
        console.error('加载可访问应用失败:', error)
      } finally {
        this.isLoading = false
      }
    }
  }
})