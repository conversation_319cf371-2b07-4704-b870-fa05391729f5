<template>
  <div class="data-mapping-config">
    <el-form label-width="120px">
      <!-- JSON/XML路径配置 -->
      <el-form-item label="数据格式">
        <el-radio-group v-model="config.dataFormat">
          <el-radio label="json">JSON</el-radio>
          <el-radio label="xml">XML</el-radio>
        </el-radio-group>
      </el-form-item>

      <!-- 路径表达式配置 -->
      <template v-if="config.dataFormat === 'json'">
        <el-form-item label="JSONPath">
          <el-input v-model="config.jsonPath" placeholder="$.store.book[0].title" />
          <div class="path-help">
            <el-link type="info" @click="showJsonPathHelp">JSONPath语法帮助</el-link>
          </div>
        </el-form-item>
      </template>

      <template v-else>
        <el-form-item label="XPath">
          <el-input v-model="config.xpath" placeholder="/store/book[1]/title" />
          <div class="path-help">
            <el-link type="info" @click="showXPathHelp">XPath语法帮助</el-link>
          </div>
        </el-form-item>
      </template>

      <!-- 字段映射配置 -->
      <el-form-item label="字段映射">
        <div v-for="(mapping, index) in config.fieldMappings" :key="index" class="mapping-item">
          <el-row :gutter="10">
            <el-col :span="8">
              <el-input v-model="mapping.source" placeholder="源字段">
                <template slot="prepend">源</template>
              </el-input>
            </el-col>
            <el-col :span="8">
              <el-input v-model="mapping.target" placeholder="目标字段">
                <template slot="prepend">目标</template>
              </el-input>
            </el-col>
            <el-col :span="6">
              <el-select v-model="mapping.transform" placeholder="转换规则">
                <el-option label="直接映射" value="direct" />
                <el-option label="类型转换" value="type" />
                <el-option label="日期格式化" value="date" />
                <el-option label="自定义函数" value="custom" />
              </el-select>
            </el-col>
            <el-col :span="2">
              <el-button type="text" icon="el-icon-delete" @click="removeMapping(index)" />
            </el-col>
          </el-row>

          <!-- 转换规则配置 -->
          <el-row v-if="mapping.transform === 'type'" class="transform-config">
            <el-col :span="24">
              <el-select v-model="mapping.targetType" placeholder="目标类型">
                <el-option label="字符串" value="string" />
                <el-option label="数字" value="number" />
                <el-option label="布尔" value="boolean" />
                <el-option label="日期" value="date" />
              </el-select>
            </el-col>
          </el-row>

          <el-row v-if="mapping.transform === 'date'" class="transform-config">
            <el-col :span="11">
              <el-input v-model="mapping.sourceFormat" placeholder="源日期格式">
                <template slot="prepend">源格式</template>
              </el-input>
            </el-col>
            <el-col :span="11" offset="2">
              <el-input v-model="mapping.targetFormat" placeholder="目标日期格式">
                <template slot="prepend">目标格式</template>
              </el-input>
            </el-col>
          </el-row>

          <el-row v-if="mapping.transform === 'custom'" class="transform-config">
            <el-col :span="24">
              <el-input
                type="textarea"
                v-model="mapping.customFunction"
                :rows="3"
                placeholder="function(value) { return value; }"
              />
            </el-col>
          </el-row>
        </div>
        <el-button type="text" icon="el-icon-plus" @click="addMapping">添加映射</el-button>
      </el-form-item>

      <!-- 默认值配置 -->
      <el-form-item label="默认值">
        <div v-for="(defaultValue, index) in config.defaultValues" :key="index" class="default-value-item">
          <el-row :gutter="10">
            <el-col :span="8">
              <el-input v-model="defaultValue.field" placeholder="字段名" />
            </el-col>
            <el-col :span="12">
              <el-input v-model="defaultValue.value" placeholder="默认值" />
            </el-col>
            <el-col :span="2">
              <el-button type="text" icon="el-icon-delete" @click="removeDefaultValue(index)" />
            </el-col>
          </el-row>
        </div>
        <el-button type="text" icon="el-icon-plus" @click="addDefaultValue">添加默认值</el-button>
      </el-form-item>
    </el-form>

    <!-- 帮助对话框 -->
    <el-dialog :title="helpDialogTitle" :visible.sync="helpDialogVisible" width="50%">
      <div v-html="helpContent"></div>
    </el-dialog>
  </div>
</template>

<script>
export default {
  name: 'DataMappingConfig',
  props: {
    value: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      config: {
        dataFormat: 'json',
        jsonPath: '',
        xpath: '',
        fieldMappings: [],
        defaultValues: []
      },
      helpDialogVisible: false,
      helpDialogTitle: '',
      helpContent: ''
    }
  },
  watch: {
    value: {
      handler(val) {
        this.config = { ...this.config, ...val }
      },
      immediate: true
    },
    config: {
      handler(val) {
        this.$emit('input', val)
      },
      deep: true
    }
  },
  methods: {
    addMapping() {
      this.config.fieldMappings.push({
        source: '',
        target: '',
        transform: 'direct'
      })
    },
    removeMapping(index) {
      this.config.fieldMappings.splice(index, 1)
    },
    addDefaultValue() {
      this.config.defaultValues.push({
        field: '',
        value: ''
      })
    },
    removeDefaultValue(index) {
      this.config.defaultValues.splice(index, 1)
    },
    showJsonPathHelp() {
      this.helpDialogTitle = 'JSONPath语法帮助'
      this.helpContent = `
        <h3>常用JSONPath语法：</h3>
        <ul>
          <li>$ - 根对象</li>
          <li>.property - 获取属性值</li>
          <li>['property'] - 获取属性值（替代写法）</li>
          <li>[index] - 获取数组索引</li>
          <li>[*] - 获取所有数组元素</li>
          <li>..property - 递归搜索属性</li>
          <li>?(@.property == value) - 条件过滤</li>
        </ul>
        <h4>示例：</h4>
        <pre>$.store.book[0].title - 获取第一本书的标题
$.store.book[*].title - 获取所有书的标题
$.store.book[?(@.price < 10)].title - 获取价格小于10的书的标题</pre>
      `
      this.helpDialogVisible = true
    },
    showXPathHelp() {
      this.helpDialogTitle = 'XPath语法帮助'
      this.helpContent = `
        <h3>常用XPath语法：</h3>
        <ul>
          <li>/ - 从根节点选取</li>
          <li>// - 从匹配选择的当前节点选择文档中的节点，不考虑它们的位置</li>
          <li>. - 选取当前节点</li>
          <li>.. - 选取当前节点的父节点</li>
          <li>@ - 选取属性</li>
          <li>* - 匹配任何元素节点</li>
        </ul>
        <h4>示例：</h4>
        <pre>/store/book[1]/title - 选择第一本书的标题
//book[price>35]/title - 选择价格高于35的书的标题
//title[@lang='eng'] - 选择所有英文标题</pre>
      `
      this.helpDialogVisible = true
    }
  }
}
</script>

<style scoped>
.mapping-item {
  margin-bottom: 15px;
  padding: 10px;
  border: 1px solid #EBEEF5;
  border-radius: 4px;
}
.transform-config {
  margin-top: 10px;
  padding-left: 20px;
}
.default-value-item {
  margin-bottom: 10px;
}
.path-help {
  margin-top: 5px;
  font-size: 12px;
}
</style> 