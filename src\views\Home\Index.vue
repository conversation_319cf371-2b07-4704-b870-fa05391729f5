<template>
  <div class="dashboard-container">
    <!-- OAuth2 应用展示区域 - 已隐藏统一门户内容 -->
    <!--
    <div class="oauth2-apps-section">
      <oauth2-apps />
    </div>
    -->

    <!-- 其他统计面板区域 -->
    <div class="panels-section">
      <!-- 暂时注释掉其他组件，等相关依赖解决后再启用 -->
      <!--
      <PanelGroup />
      <LineChart />
      <PieChart />
      <RaddarChart />
      <BarChart />
      -->

      <!-- 临时显示内容，替代统一门户 -->
      <div class="welcome-section">
        <el-card class="welcome-card">
          <template #header>
            <div class="card-header">
              <span>欢迎使用系统</span>
            </div>
          </template>
          <div class="welcome-content">
            <p>欢迎来到 XIOT SE 前端系统！</p>
            <p>统一门户内容已隐藏，您可以通过左侧菜单访问各个功能模块。</p>
          </div>
        </el-card>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, onMounted } from 'vue'
import { ElCard } from 'element-plus'
import { useUserStore } from '@/store/modules/user'

// OAuth2Apps 组件已注释，不再导入
// import OAuth2Apps from './components/OAuth2Apps.vue'

// 暂时注释掉其他组件导入
/*
import PanelGroup from './components/PanelGroup.vue'
import LineChart from './components/LineChart.vue'
import PieChart from './components/PieChart.vue'
import RaddarChart from './components/RaddarChart.vue'
import BarChart from './components/BarChart.vue'
*/

const userStore = useUserStore()

// 计算属性
const userInfo = computed(() => ({
  name: userStore.getUser?.nickname || '',
  id: userStore.getUser?.id || ''
}))

// 移除 roles 相关代码，因为 UserVO 类型中没有 roles 属性
// const userRoles = computed(() => userStore.getUser?.roles || [])

onMounted(() => {
  console.log('首页组件已挂载')
  console.log('用户信息:', userInfo.value)
  // console.log('用户角色:', userRoles.value)
})
</script>

<style lang="scss" scoped>
.dashboard-container {
  padding: 20px;
}

.oauth2-apps-section {
  margin-bottom: 20px;
}

.panels-section {
  // 为未来的统计面板预留样式
}
</style>

