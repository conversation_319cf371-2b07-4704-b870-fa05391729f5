<template>
  <div class="dashboard-container">
    <!-- OAuth2 应用展示区域 -->
    <div class="oauth2-apps-section">
      <oauth2-apps />
    </div>
    
    <!-- 其他统计面板区域 -->
    <div class="panels-section">
      <!-- 暂时注释掉其他组件，等相关依赖解决后再启用 -->
      <!--
      <PanelGroup />
      <LineChart />
      <PieChart />
      <RaddarChart />
      <BarChart />
      -->
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, onMounted } from 'vue'
import { useUserStore } from '@/store/modules/user'
import OAuth2Apps from './components/OAuth2Apps.vue'

// 暂时注释掉其他组件导入
/*
import PanelGroup from './components/PanelGroup.vue'
import LineChart from './components/LineChart.vue'
import PieChart from './components/PieChart.vue'
import RaddarChart from './components/RaddarChart.vue'
import BarChart from './components/BarChart.vue'
*/

const userStore = useUserStore()

// 计算属性
const userInfo = computed(() => ({
  name: userStore.getUser?.nickname || '',
  id: userStore.getUser?.id || ''
}))

const userRoles = computed(() => userStore.getUser?.roles || [])

onMounted(() => {
  console.log('首页组件已挂载')
  console.log('用户信息:', userInfo.value)
  console.log('用户角色:', userRoles.value)
})
</script>

<style lang="scss" scoped>
.dashboard-container {
  padding: 20px;
}

.oauth2-apps-section {
  margin-bottom: 20px;
}

.panels-section {
  // 为未来的统计面板预留样式
}
</style>

