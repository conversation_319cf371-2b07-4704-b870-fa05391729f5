import request from '@/utils/request'

// 查询转换列表
export function getTransformPage(query) {
  return request({
    url: '/etl/transform/page',
    method: 'get',
    params: query
  })
}

// 查询转换详情
export function getTransform(id) {
  return request({
    url: '/etl/transform/get',
    method: 'get',
    params: { id }
  })
}

// 创建转换
export function createTransform(data) {
  return request({
    url: '/etl/transform/create',
    method: 'post',
    params: data
  })
}

// 更新转换
export function updateTransform(data) {
  return request({
    url: '/etl/transform/update',
    method: 'put',
    params: data
  })
}

// 删除转换
export function deleteTransform(id) {
  return request({
    url: '/etl/transform/delete',
    method: 'delete',
    params: { id }
  })
}

// 执行转换
export function executeTransform(id) {
  return request({
    url: '/etl/transform/execute',
    method: 'post',
    params: { id }
  })
}

// 导出转换数据
export function exportTransform(query) {
  return request({
    url: '/etl/transform/export',
    method: 'get',
    params: query,
    responseType: 'blob'
  })
}

// 获取转换的简单列表
export function getSimpleTransformList() {
  return request({
    url: '/etl/transform/list-all-simple',
    method: 'get'
  })
} 