export default {
  path: '/esb',
  component: () => import('@/layout'),
  name: 'ESB',
  meta: {
    title: 'ESB企业服务总线',
    icon: 'ep:connection'
  },
  children: [
    {
      path: 'api',
      component: () => import('@/views/esb/api/index'),
      name: 'EsbApi',
      meta: {
        title: '接口管理',
        icon: 'ep:api'
      }
    },
    {
      path: 'monitor',
      component: () => import('@/views/esb/monitor/index'),
      name: 'EsbMonitor',
      meta: {
        title: '接口监控',
        icon: 'ep:monitor'
      }
    },
    {
      path: 'log',
      component: () => import('@/views/esb/log/index'),
      name: 'EsbLog',
      meta: {
        title: '接口日志',
        icon: 'ep:document'
      }
    }
  ]
} 