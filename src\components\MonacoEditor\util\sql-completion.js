const keywords = [
  "SELECT", "FROM", "WHERE", "AND", "OR", "ORDER BY", "GROUP BY", "HAVING",
  "INNER JOIN", "LEFT JOIN", "RIGHT JOIN", "OUTER JOIN", "ON", "AS",
  "INSERT", "INTO", "VALUES", "UPDATE", "SET", "DELETE", "CREATE", "TABLE",
  "DROP", "ALTER", "INDEX", "PRIMARY KEY", "FOREIGN KEY", "NOT NULL",
  "DISTINCT", "BETWEEN", "IN", "LIKE", "IS NULL", "IS NOT NULL", "ASC", "DESC",
  "COUNT", "SUM", "AVG", "MAX", "MIN", "LIMIT", "OFFSET"
];

const operators = [
  "+", "-", "*", "/", "%", "=", ">", "<", ">=", "<=", "<>", "!=",
  "AND", "OR", "NOT", "LIKE", "IN", "BETWEEN", "IS", "NULL"
];

const functions = [
  "COUNT", "SUM", "AVG", "MAX", "MIN", "UPPER", "LOWER", "TRIM",
  "SUBSTRING", "CONCAT", "NOW", "DATE", "YEAR", "MONTH", "DAY",
  "ROUND", "FLOOR", "CEILING", "COALESCE", "NULLIF", "CASE", "WHEN", "THEN", "ELSE", "END"
];

export default function createSqlCompleter(getHints) {
  return {
    triggerCharacters: [" ", ".", ","],
    provideCompletionItems: function(model, position) {
      const textUntilPosition = model.getValueInRange({
        startLineNumber: position.lineNumber,
        startColumn: 1,
        endLineNumber: position.lineNumber,
        endColumn: position.column
      });

      const suggestions = [];

      // 添加关键字建议
      keywords.forEach(keyword => {
        suggestions.push({
          label: keyword,
          kind: monaco.languages.CompletionItemKind.Keyword,
          insertText: keyword,
          detail: "SQL关键字"
        });
      });

      // 添加操作符建议
      operators.forEach(operator => {
        suggestions.push({
          label: operator,
          kind: monaco.languages.CompletionItemKind.Operator,
          insertText: operator,
          detail: "SQL操作符"
        });
      });

      // 添加函数建议
      functions.forEach(func => {
        suggestions.push({
          label: func,
          kind: monaco.languages.CompletionItemKind.Function,
          insertText: func,
          detail: "SQL函数"
        });
      });

      // 添加自定义提示
      const hints = getHints(model);
      hints.forEach(hint => {
        suggestions.push({
          label: hint.label || hint,
          kind: hint.kind || monaco.languages.CompletionItemKind.Text,
          insertText: hint.insertText || hint.label || hint,
          detail: hint.detail || "自定义提示"
        });
      });

      return {
        suggestions: suggestions
      };
    }
  };
} 