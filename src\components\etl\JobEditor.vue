<template>
  <div class="job-editor">
    <div class="job-header">
      <div class="left-tools">
        <el-button-group>
          <el-button type="primary" size="small" @click="saveJob">保存</el-button>
          <el-button type="success" size="small" @click="runJob">运行</el-button>
          <el-button type="warning" size="small" @click="stopJob">停止</el-button>
        </el-button-group>
      </div>
      <div class="right-tools">
        <el-button-group>
          <el-button size="small" @click="verifyJob">校验</el-button>
        </el-button-group>
      </div>
    </div>
    <div class="job-content">
      <div class="left-panel">
        <div class="panel-title">作业组件</div>
        <div class="component-list">
          <el-collapse v-model="activeCategory">
            <el-collapse-item 
              v-for="(components, category) in jobComponentList" 
              :key="category"
              :title="category"
              :name="category">
              <div 
                v-for="item in components"
                :key="item.type"
                class="component-item"
                draggable="true"
                @dragstart="handleDragStart(item)">
                <i :class="item.icon"></i>
                <span>{{ item.name }}</span>
              </div>
            </el-collapse-item>
          </el-collapse>
        </div>
      </div>
      <div class="center-panel">
        <flow-main 
          ref="flowMain"
          :type="'job'"
          @nodeSelect="handleNodeSelect" />
      </div>
      <div class="right-panel" v-if="currentNode">
        <div class="panel-title">作业节点配置</div>
        <div class="panel-content">
          <el-form label-position="top" size="small">
            <el-form-item label="节点名称">
              <el-input v-model="currentNode.name" />
            </el-form-item>
            <el-form-item label="节点描述">
              <el-input type="textarea" v-model="currentNode.description" />
            </el-form-item>
          </el-form>

          <component
            :is="getJobComponent(currentNode.type)"
            v-if="currentNode.type"
            v-model="currentNode.config"
            @update:config="updateNodeConfig"
          />
        </div>
      </div>
    </div>
    <!-- 执行日志面板 -->
    <div class="job-log" v-show="showLog">
      <div class="log-header">
        <span>执行日志</span>
        <el-button type="text" @click="showLog = false">关闭</el-button>
      </div>
      <div class="log-content">
        <pre>{{ executionLog }}</pre>
      </div>
    </div>
  </div>
</template>

<script>
import FlowMain from './core/FlowMain.vue'
import { v4 as uuidv4 } from 'uuid'
import { save, run, stop, verify, executeResult } from '@/api/etl/job'

export default {
  name: 'JobEditor',
  components: {
    FlowMain
  },
  data() {
    return {
      activeCategory: ['通用', '文件', '数据库', '条件', '脚本'],
      currentNode: null,
      dragComponent: null,
      showLog: false,
      executionLog: '',
      jobComponentList: {
        '通用': [
          { type: 'Start', name: '开始', icon: 'el-icon-video-play' },
          { type: 'Success', name: '成功', icon: 'el-icon-check' },
          { type: 'Fail', name: '失败', icon: 'el-icon-close' },
          { type: 'Abort', name: '中止', icon: 'el-icon-circle-close' }
        ],
        '文件': [
          { type: 'FileExists', name: '检查文件', icon: 'el-icon-document' },
          { type: 'DeleteFile', name: '删除文件', icon: 'el-icon-document-delete' },
          { type: 'CreateFolder', name: '创建目录', icon: 'el-icon-folder-add' }
        ],
        '数据库': [
          { type: 'CheckConnection', name: '检查连接', icon: 'el-icon-connection' },
          { type: 'ExecuteSQL', name: '执行SQL', icon: 'el-icon-data-line' }
        ],
        '条件': [
          { type: 'IfExists', name: '条件判断', icon: 'el-icon-switch-button' },
          { type: 'EvalCondition', name: '条件求值', icon: 'el-icon-reading' }
        ],
        '脚本': [
          { type: 'Shell', name: 'Shell脚本', icon: 'el-icon-edit-outline' },
          { type: 'JavaScript', name: 'JavaScript', icon: 'el-icon-edit' }
        ]
      }
    }
  },
  methods: {
    handleDragStart(component) {
      this.dragComponent = {
        ...component,
        id: uuidv4(),
        top: 0,
        left: 0
      }
    },
    handleNodeSelect(node) {
      this.currentNode = node
    },
    async saveJob() {
      try {
        const jobData = this.$refs.flowMain.getFlowData()
        await save(jobData)
        this.$message.success('保存成功')
      } catch (error) {
        this.$message.error('保存失败：' + error.message)
      }
    },
    async runJob() {
      try {
        const jobData = this.$refs.flowMain.getFlowData()
        await run(jobData)
        this.showLog = true
        this.pollExecutionResult()
      } catch (error) {
        this.$message.error('运行失败：' + error.message)
      }
    },
    async stopJob() {
      try {
        const jobData = this.$refs.flowMain.getFlowData()
        await stop(jobData)
        this.$message.success('已停止作业')
      } catch (error) {
        this.$message.error('停止失败：' + error.message)
      }
    },
    async verifyJob() {
      try {
        const jobData = this.$refs.flowMain.getFlowData()
        const result = await verify(jobData)
        if (result.valid) {
          this.$message.success('校验通过')
        } else {
          this.$message.warning('校验不通过：' + result.message)
        }
      } catch (error) {
        this.$message.error('校验失败：' + error.message)
      }
    },
    async pollExecutionResult() {
      try {
        const result = await executeResult()
        this.executionLog = result.log
        if (result.status === 'RUNNING') {
          setTimeout(() => this.pollExecutionResult(), 1000)
        }
      } catch (error) {
        console.error('获取执行结果失败：', error)
      }
    },
    getJobComponent(type) {
      // TODO: 根据作业节点类型返回对应的配置组件
      return null
    },
    updateNodeConfig(config) {
      if (this.currentNode) {
        this.currentNode.config = config
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.job-editor {
  height: 100%;
  display: flex;
  flex-direction: column;

  .job-header {
    height: 50px;
    padding: 8px;
    border-bottom: 1px solid #dcdfe6;
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  .job-content {
    flex: 1;
    display: flex;
    overflow: hidden;

    .left-panel {
      width: 200px;
      border-right: 1px solid #dcdfe6;
      display: flex;
      flex-direction: column;

      .panel-title {
        height: 40px;
        line-height: 40px;
        padding: 0 16px;
        font-weight: bold;
        background-color: #f5f7fa;
        border-bottom: 1px solid #dcdfe6;
      }

      .component-list {
        flex: 1;
        overflow-y: auto;
        padding: 8px;

        .el-collapse {
          border: none;
        }

        .component-item {
          height: 32px;
          line-height: 32px;
          padding: 0 8px;
          margin-bottom: 4px;
          border: 1px dashed #dcdfe6;
          border-radius: 4px;
          cursor: move;
          user-select: none;
          display: flex;
          align-items: center;

          &:hover {
            background-color: #ecf5ff;
            border-color: #409eff;
          }

          i {
            margin-right: 8px;
            font-size: 16px;
            color: #666;
          }
        }
      }
    }

    .center-panel {
      flex: 1;
      background-color: #fff;
      position: relative;
      overflow: hidden;
    }

    .right-panel {
      width: 300px;
      border-left: 1px solid #dcdfe6;
      display: flex;
      flex-direction: column;

      .panel-title {
        height: 40px;
        line-height: 40px;
        padding: 0 16px;
        font-weight: bold;
        background-color: #f5f7fa;
        border-bottom: 1px solid #dcdfe6;
      }

      .panel-content {
        flex: 1;
        overflow-y: auto;
        padding: 16px;
      }
    }
  }

  .job-log {
    height: 200px;
    border-top: 1px solid #dcdfe6;
    display: flex;
    flex-direction: column;

    .log-header {
      height: 40px;
      line-height: 40px;
      padding: 0 16px;
      background-color: #f5f7fa;
      border-bottom: 1px solid #dcdfe6;
      display: flex;
      justify-content: space-between;
      align-items: center;
    }

    .log-content {
      flex: 1;
      overflow-y: auto;
      padding: 8px;
      background-color: #1e1e1e;
      color: #fff;
      font-family: monospace;

      pre {
        margin: 0;
        white-space: pre-wrap;
        word-wrap: break-word;
      }
    }
  }
}
</style>
