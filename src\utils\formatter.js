/**
 * 数字格式化
 * @param {number} num 数字
 * @param {number} precision 精度
 * @returns {string} 格式化后的数字
 */
export function formatNumber(num, precision = 2) {
  return Number(num).toFixed(precision)
}

/**
 * 金额格式化
 * @param {number} amount 金额
 * @param {string} currency 货币符号
 * @returns {string} 格式化后的金额
 */
export function formatAmount(amount, currency = '¥') {
  return currency + amount.toFixed(2).replace(/\d(?=(\d{3})+\.)/g, '$&,')
}

/**
 * 百分比格式化
 * @param {number} value 值
 * @param {number} total 总数
 * @returns {string} 格式化后的百分比
 */
export function formatPercent(value, total) {
  if (total === 0) return '0%'
  return ((value / total) * 100).toFixed(2) + '%'
}

/**
 * 日期时间格式化
 * @param {Date|string|number} time 日期时间
 * @param {string} pattern 格式化模式
 * @returns {string} 格式化后的日期时间
 */
export function formatDateTime(time, pattern = 'YYYY-MM-DD HH:mm:ss') {
  if (!time) return ''
  let date = null
  if (typeof time === 'object') {
    date = time
  } else {
    if (typeof time === 'string' && /^[0-9]+$/.test(time)) {
      time = parseInt(time)
    } else if (typeof time === 'string') {
      time = time.replace(new RegExp(/-/gm), '/')
    }
    if (typeof time === 'number' && time.toString().length === 10) {
      time = time * 1000
    }
    date = new Date(time)
  }
  const formatObj = {
    YYYY: date.getFullYear(),
    MM: date.getMonth() + 1,
    DD: date.getDate(),
    HH: date.getHours(),
    mm: date.getMinutes(),
    ss: date.getSeconds(),
    A: date.getHours() < 12 ? 'AM' : 'PM'
  }
  return pattern.replace(/(YYYY|MM|DD|HH|mm|ss|A)/g, (result) => {
    let value = formatObj[result]
    if (result === 'A') return value
    if (value < 10) {
      value = '0' + value
    }
    return value || 0
  })
}

/**
 * 相对时间格式化
 * @param {Date|string|number} time 时间
 * @returns {string} 格式化后的相对时间
 */
export function formatRelativeTime(time) {
  if (!time) return ''
  const d = new Date(time)
  const now = Date.now()
  const diff = (now - d) / 1000
  if (diff < 30) {
    return '刚刚'
  } else if (diff < 3600) {
    return Math.ceil(diff / 60) + '分钟前'
  } else if (diff < 3600 * 24) {
    return Math.ceil(diff / 3600) + '小时前'
  } else if (diff < 3600 * 24 * 2) {
    return '1天前'
  }
  return formatDateTime(time, 'YYYY-MM-DD')
}

/**
 * 文本截断
 * @param {string} str 文本
 * @param {number} len 长度
 * @returns {string} 截断后的文本
 */
export function truncateText(str, len) {
  if (!str) return ''
  if (str.length > len) {
    return str.substring(0, len) + '...'
  }
  return str
}

/**
 * 数字转中文
 * @param {number} num 数字
 * @returns {string} 中文数字
 */
export function numberToChinese(num) {
  const units = ['', '十', '百', '千', '万', '十万', '百万', '千万', '亿']
  const chars = ['零', '一', '二', '三', '四', '五', '六', '七', '八', '九']
  const arr = String(num).split('').map(n => chars[n])
  let result = ''
  for (let i = 0; i < arr.length; i++) {
    const unit = units[arr.length - 1 - i]
    result += arr[i] + unit
  }
  return result.replace(/零[十百千]/g, '零').replace(/零+/g, '零').replace(/零$/, '')
}

/**
 * 格式化文件大小
 * @param {number} size 文件大小（字节）
 * @returns {string} 格式化后的文件大小
 */
export function formatFileSize(size) {
  if (size === 0) return '0 B'
  const k = 1024
  const sizes = ['B', 'KB', 'MB', 'GB', 'TB', 'PB', 'EB', 'ZB', 'YB']
  const i = Math.floor(Math.log(size) / Math.log(k))
  return (size / Math.pow(k, i)).toPrecision(3) + ' ' + sizes[i]
}

/**
 * 格式化手机号
 * @param {string} phone 手机号
 * @returns {string} 格式化后的手机号
 */
export function formatPhone(phone) {
  return phone.replace(/(\d{3})\d{4}(\d{4})/, '$1****$2')
}

/**
 * 格式化身份证号
 * @param {string} idCard 身份证号
 * @returns {string} 格式化后的身份证号
 */
export function formatIdCard(idCard) {
  return idCard.replace(/(\d{6})\d{8}(\d{4})/, '$1********$2')
} 