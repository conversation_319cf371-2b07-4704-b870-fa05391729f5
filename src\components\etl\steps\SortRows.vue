<template>
  <div class="sort-rows-config">
    <el-form label-position="top" size="small">
      <el-form-item label="排序字段">
        <div class="sort-fields">
          <div class="fields-table">
            <div class="header">
              <div class="col">字段</div>
              <div class="col">排序方向</div>
              <div class="col">大小写敏感</div>
              <div class="col">操作</div>
            </div>
            <div 
              v-for="(field, index) in config.sortFields" 
              :key="index"
              class="row">
              <div class="col">
                <el-select v-model="field.name" placeholder="选择字段">
                  <el-option
                    v-for="f in inputFields"
                    :key="f.name"
                    :label="f.name"
                    :value="f.name"
                  />
                </el-select>
              </div>
              <div class="col">
                <el-select v-model="field.direction">
                  <el-option label="升序" value="ASC" />
                  <el-option label="降序" value="DESC" />
                </el-select>
              </div>
              <div class="col">
                <el-checkbox v-model="field.caseSensitive" />
              </div>
              <div class="col">
                <el-button-group>
                  <el-button 
                    type="text" 
                    icon="el-icon-top"
                    :disabled="index === 0"
                    @click="moveField(index, -1)" />
                  <el-button 
                    type="text" 
                    icon="el-icon-bottom"
                    :disabled="index === config.sortFields.length - 1"
                    @click="moveField(index, 1)" />
                  <el-button 
                    type="text" 
                    icon="el-icon-delete"
                    @click="removeField(index)" />
                </el-button-group>
              </div>
            </div>
          </div>
          <el-button 
            type="text"
            icon="el-icon-plus"
            @click="addField">
            添加排序字段
          </el-button>
        </div>
      </el-form-item>

      <el-form-item label="临时文件目录">
        <el-input v-model="config.tempDirectory" placeholder="临时文件目录路径">
          <el-button slot="append" icon="el-icon-folder-opened" @click="selectDirectory">
            选择目录
          </el-button>
        </el-input>
      </el-form-item>

      <el-form-item label="每组最大记录数">
        <el-input-number 
          v-model="config.maxRecordsPerGroup"
          :min="1000"
          :max="1000000"
          :step="1000"
        />
      </el-form-item>

      <el-form-item>
        <el-checkbox v-model="config.onlyPassingUniqueRows">
          只传递唯一行
        </el-checkbox>
      </el-form-item>

      <el-form-item>
        <el-checkbox v-model="config.compressFiles">
          压缩临时文件
        </el-checkbox>
      </el-form-item>
    </el-form>
  </div>
</template>

<script>
export default {
  name: 'SortRows',
  props: {
    value: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      config: {
        sortFields: [],
        tempDirectory: '',
        maxRecordsPerGroup: 10000,
        onlyPassingUniqueRows: false,
        compressFiles: true
      },
      inputFields: []
    }
  },
  watch: {
    value: {
      handler(val) {
        this.config = { ...this.config, ...val }
      },
      immediate: true,
      deep: true
    },
    config: {
      handler(val) {
        this.$emit('input', val)
      },
      deep: true
    }
  },
  methods: {
    addField() {
      this.config.sortFields.push({
        name: '',
        direction: 'ASC',
        caseSensitive: true
      })
    },
    removeField(index) {
      this.config.sortFields.splice(index, 1)
    },
    moveField(index, direction) {
      const newIndex = index + direction
      if (newIndex < 0 || newIndex >= this.config.sortFields.length) return
      
      const field = this.config.sortFields[index]
      this.config.sortFields.splice(index, 1)
      this.config.sortFields.splice(newIndex, 0, field)
    },
    selectDirectory() {
      // TODO: 实现目录选择功能
    }
  },
  async created() {
    try {
      // TODO: 获取输入字段列表
    } catch (error) {
      console.error('获取字段失败：', error)
    }
  }
}
</script>

<style lang="scss" scoped>
.sort-rows-config {
  padding: 16px;

  .sort-fields {
    border: 1px solid #dcdfe6;
    border-radius: 4px;
    padding: 16px;

    .fields-table {
      .header, .row {
        display: grid;
        grid-template-columns: 2fr 1fr 1fr 1fr;
        gap: 8px;
        align-items: center;
        padding: 8px 0;
        
        &:not(:last-child) {
          border-bottom: 1px solid #ebeef5;
        }
      }

      .header {
        font-weight: bold;
      }

      .row {
        .col {
          display: flex;
          align-items: center;
          justify-content: center;

          .el-select {
            width: 100%;
          }
        }
      }
    }
  }
}
</style>
