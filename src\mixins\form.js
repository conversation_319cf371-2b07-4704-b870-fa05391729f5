export default {
  data() {
    return {
      // 表单参数
      form: {},
      // 表单校验
      rules: {},
      // 状态数据
      statusOptions: [
        {
          value: '0',
          label: '正常'
        },
        {
          value: '1',
          label: '停用'
        }
      ],
      // 是否显示弹出层
      open: false,
      // 弹出层标题
      title: '',
      // 加载状态
      loading: false
    }
  },
  methods: {
    /** 表单重置 */
    reset() {
      this.form = {
        id: undefined,
        status: '0'
      }
      if (this.$refs.form) {
        this.$refs.form.resetFields()
      }
    },
    /** 取消按钮 */
    cancel() {
      this.open = false
      this.reset()
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs.form.validate(valid => {
        if (valid) {
          this.loading = true
          const submitMethod = this.form.id ? this.updateApi : this.addApi
          submitMethod(this.form).then(() => {
            this.$modal.msgSuccess(this.form.id ? '修改成功' : '新增成功')
            this.open = false
            this.$emit('refresh')
          }).finally(() => {
            this.loading = false
          })
        }
      })
    },
    /** 表单数据回显 */
    setFormData(data) {
      Object.keys(this.form).forEach(key => {
        this.form[key] = data[key] !== undefined ? data[key] : this.form[key]
      })
    },
    /** 字典数据翻译 */
    selectDictLabel(datas, value) {
      if (value === undefined) {
        return ''
      }
      const data = datas.find(item => item.value === value)
      return data ? data.label : ''
    },
    /** 处理时间范围 */
    getDateRange(dateRange) {
      if (dateRange) {
        return {
          beginTime: dateRange[0],
          endTime: dateRange[1]
        }
      }
      return {
        beginTime: undefined,
        endTime: undefined
      }
    }
  }
} 