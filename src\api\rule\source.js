import request from '@/utils/request'

// 创建数据源
export function createSource(data) {
  return request({
    url: '/metadata/source',
    method: 'post',
    data: data
  })
}

// 更新数据源
export function updateSource(data) {
  return request({
    url: '/metadata/source',
    method: 'put',
    data: data
  })
}

// 删除数据源
export function deleteSource(id) {
  return request({
    url: `/metadata/source/${id}`,
    method: 'delete'
  })
}

// 获取数据源
export function getSource(id) {
  return request({
    url: `/metadata/source/${id}`,
    method: 'get'
  })
}

// 获取数据源分页
export function getSourcePage(query) {
  return request({
    url: '/metadata/source/page',
    method: 'get',
    params: query
  })
}

// 获取数据源列表
export function getSourceList() {
  return request({
    url: '/metadata/source/list',
    method: 'get'
  })
}

// 刷新数据源
export function refreshSource(id) {
  return request({
    url: `/metadata/source/refresh/${id}`,
    method: 'post'
  })
}

// 同步数据源
export function syncSource(id) {
  return request({
    url: `/metadata/source/sync/${id}`,
    method: 'post'
  })
}

// 检查数据源连接
export function checkSource(data) {
  return request({
    url: '/metadata/source/check',
    method: 'post',
    data: data
  })
}

// 导出数据源文档
export function exportSourceWord(id) {
  return request({
    url: `/metadata/source/word/${id}`,
    method: 'get',
    responseType: 'blob'
  })
} 