import request from '@/utils/request'

// 获取仪表盘列表
export function listDashboard(query) {
  return request({
    url: '/report/bi/dashboard/list',
    method: 'get',
    params: query
  })
}

// 获取仪表盘详情
export function getDashboard(id) {
  return request({
    url: `/report/bi/dashboard/${id}`,
    method: 'get'
  })
}

// 新建仪表盘
export function createDashboard(data) {
  return request({
    url: '/report/bi/dashboard',
    method: 'post',
    data
  })
}

// 更新仪表盘
export function updateDashboard(data) {
  return request({
    url: '/report/bi/dashboard',
    method: 'put',
    data
  })
}

// 删除仪表盘
export function deleteDashboard(id) {
  return request({
    url: `/report/bi/dashboard/${id}`,
    method: 'delete'
  })
}

// 导出仪表盘
export function exportDashboard(id) {
  return request({
    url: `/report/bi/dashboard/export/${id}`,
    method: 'get',
    responseType: 'blob'
  })
}

// 导入仪表盘
export function importDashboard(data) {
  return request({
    url: '/report/bi/dashboard/import',
    method: 'post',
    data
  })
}

// 获取仪表盘数据
export function getDashboardData(id, params) {
  return request({
    url: `/report/bi/dashboard/data/${id}`,
    method: 'get',
    params
  })
}

// 保存为模板
export function saveTemplate(data) {
  return request({
    url: '/report/bi/dashboard/template',
    method: 'post',
    data
  })
}

// 获取模板列表
export function listTemplate(query) {
  return request({
    url: '/report/bi/dashboard/template/list',
    method: 'get',
    params: query
  })
}

// 应用模板
export function applyTemplate(templateId) {
  return request({
    url: `/report/bi/dashboard/template/${templateId}/apply`,
    method: 'post'
  })
}

// 获取图表数据
export function getChartData(params) {
  return request({
    url: '/report/bi/dashboard/chart/data',
    method: 'get',
    params
  })
}

// 保存仪表盘布局
export function saveLayout(id, data) {
  return request({
    url: `/report/bi/dashboard/${id}/layout`,
    method: 'put',
    data
  })
} 