import request from '@/utils/request';

// 查询转换列表
export function listTransform(query) {
  return request({
    url: '/etl/transform/page',
    method: 'get',
    params: query
  });
}

// 查询转换详情
export function getTransform(id) {
  return request({
    url: '/etl/transform/get',
    method: 'get',
    params: { id }
  });
}

// 新增转换
export function addTransform(data) {
  return request({
    url: '/etl/transform/create',
    method: 'post',
    params: data
  });
}

// 修改转换
export function updateTransform(data) {
  return request({
    url: '/etl/transform/update',
    method: 'put',
    params: data
  });
}

// 删除转换
export function delTransform(id) {
  return request({
    url: '/etl/transform/delete',
    method: 'delete',
    params: { id }
  });
}

// 导出转换
export function exportTransform(query) {
  return request({
    url: '/etl/transform/export',
    method: 'get',
    params: query,
    responseType: 'blob'
  });
}

// 执行转换
export function executeTransform(id) {
  return request({
    url: '/etl/transform/execute',
    method: 'post',
    params: { id }
  });
}

// 获取转换简单列表
export function listSimpleTransform() {
  return request({
    url: '/etl/transform/list-all-simple',
    method: 'get'
  });
}

// 更新转换状态
export function updateTransformStatus(id, status) {
  return request({
    url: '/etl/transform/update-status',
    method: 'put',
    params: { id, status }
  });
}

// 生成转换文件
export function generateTransformFile(id) {
  return request({
    url: '/etl/transform/generate-file',
    method: 'post',
    params: { id }
  });
}
