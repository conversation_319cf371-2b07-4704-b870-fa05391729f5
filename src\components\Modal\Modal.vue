<template>
  <transition name="modal-fade">
    <div class="modal-backdrop" v-show="show">
      <div class="modal" @click.stop>
        <div class="modal-header">
          <slot name="header">
            <h3>{{ title }}</h3>
            <el-button class="close-btn" @click="$emit('close')">
              <i class="el-icon-close"></i>
            </el-button>
          </slot>
        </div>

        <div class="modal-body">
          <slot name="body"></slot>
        </div>

        <div class="modal-footer" v-if="showFooter">
          <slot name="footer">
            <el-button @click="$emit('close')">{{ cancelText }}</el-button>
            <el-button type="primary" @click="$emit('confirm')">{{ confirmText }}</el-button>
          </slot>
        </div>
      </div>
    </div>
  </transition>
</template>

<script>
export default {
  name: "Modal",
  props: {
    show: {
      type: Boolean,
      default: false
    },
    title: {
      type: String,
      default: "标题"
    },
    showFooter: {
      type: Boolean,
      default: true
    },
    confirmText: {
      type: String,
      default: "确定"
    },
    cancelText: {
      type: String,
      default: "取消"
    },
    width: {
      type: String,
      default: "50%"
    }
  }
};
</script>

<style lang="scss" scoped>
.modal-backdrop {
  position: fixed;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  background-color: rgba(0, 0, 0, 0.3);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 2000;
}

.modal {
  background: #fff;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.33);
  border-radius: 4px;
  width: v-bind(width);
  max-width: 90%;
  max-height: 90vh;
  display: flex;
  flex-direction: column;
  
  .modal-header {
    padding: 20px;
    border-bottom: 1px solid #dcdfe6;
    display: flex;
    justify-content: space-between;
    align-items: center;

    h3 {
      margin: 0;
      font-size: 18px;
      color: #303133;
    }

    .close-btn {
      padding: 0;
      background: transparent;
      border: none;
      font-size: 20px;
      color: #909399;
      cursor: pointer;

      &:hover {
        color: #409eff;
      }
    }
  }

  .modal-body {
    padding: 20px;
    overflow-y: auto;
    flex: 1;
  }

  .modal-footer {
    padding: 20px;
    border-top: 1px solid #dcdfe6;
    text-align: right;

    .el-button + .el-button {
      margin-left: 10px;
    }
  }
}

.modal-fade-enter,
.modal-fade-leave-active {
  opacity: 0;
}

.modal-fade-enter-active,
.modal-fade-leave-active {
  transition: opacity 0.3s ease;
}
</style> 