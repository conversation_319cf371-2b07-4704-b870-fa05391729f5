import request from '@/utils/request';

// 查询转换规则列表
export function listTransformRule(transformId) {
  return request({
    url: '/etl/transform/rule/list',
    method: 'get',
    params: { transformId }
  });
}

// 查询转换规则详情
export function getTransformRule(id) {
  return request({
    url: '/etl/transform/rule/get',
    method: 'get',
    params: { id }
  });
}

// 新增转换规则
export function addTransformRule(data) {
  return request({
    url: '/etl/transform/rule/create',
    method: 'post',
    data
  });
}

// 修改转换规则
export function updateTransformRule(data) {
  return request({
    url: '/etl/transform/rule/update',
    method: 'put',
    data
  });
}

// 删除转换规则
export function delTransformRule(id) {
  return request({
    url: '/etl/transform/rule/delete',
    method: 'delete',
    params: { id }
  });
} 