<template>
  <div class="protocol-config">
    <el-form label-width="120px">
      <el-form-item label="源协议">
        <el-select v-model="config.sourceProtocol">
          <el-option label="HTTP" value="http" />
          <el-option label="SOAP" value="soap" />
          <el-option label="JMS" value="jms" />
          <el-option label="REST" value="rest" />
        </el-select>
      </el-form-item>

      <el-form-item label="目标协议">
        <el-select v-model="config.targetProtocol">
          <el-option label="HTTP" value="http" />
          <el-option label="SOAP" value="soap" />
          <el-option label="JMS" value="jms" />
          <el-option label="REST" value="rest" />
        </el-select>
      </el-form-item>

      <!-- SOAP配置 -->
      <template v-if="config.sourceProtocol === 'soap' || config.targetProtocol === 'soap'">
        <el-form-item label="WSDL地址">
          <el-input v-model="config.wsdlUrl" placeholder="http://example.com/service?wsdl" />
        </el-form-item>
        <el-form-item label="服务名称">
          <el-input v-model="config.serviceName" />
        </el-form-item>
        <el-form-item label="端口名称">
          <el-input v-model="config.portName" />
        </el-form-item>
      </template>

      <!-- JMS配置 -->
      <template v-if="config.sourceProtocol === 'jms' || config.targetProtocol === 'jms'">
        <el-form-item label="队列名称">
          <el-input v-model="config.queueName" />
        </el-form-item>
        <el-form-item label="消息类型">
          <el-select v-model="config.messageType">
            <el-option label="Text" value="text" />
            <el-option label="Object" value="object" />
            <el-option label="Map" value="map" />
            <el-option label="Bytes" value="bytes" />
          </el-select>
        </el-form-item>
      </template>

      <!-- 数据转换配置 -->
      <el-form-item label="数据转换">
        <el-select v-model="config.dataTransform">
          <el-option label="JSON转XML" value="json2xml" />
          <el-option label="XML转JSON" value="xml2json" />
          <el-option label="自定义转换" value="custom" />
        </el-select>
      </el-form-item>

      <!-- 自定义转换配置 -->
      <template v-if="config.dataTransform === 'custom'">
        <el-form-item label="转换脚本">
          <el-input
            type="textarea"
            v-model="config.transformScript"
            :rows="6"
            placeholder="请输入数据转换脚本"
          />
        </el-form-item>
      </template>

      <!-- 字符编码配置 -->
      <el-form-item label="字符编码">
        <el-select v-model="config.charset">
          <el-option label="UTF-8" value="UTF-8" />
          <el-option label="GBK" value="GBK" />
          <el-option label="ISO-8859-1" value="ISO-8859-1" />
        </el-select>
      </el-form-item>
    </el-form>
  </div>
</template>

<script>
export default {
  name: 'ProtocolConfig',
  props: {
    value: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      config: {
        sourceProtocol: '',
        targetProtocol: '',
        wsdlUrl: '',
        serviceName: '',
        portName: '',
        queueName: '',
        messageType: 'text',
        dataTransform: '',
        transformScript: '',
        charset: 'UTF-8'
      }
    }
  },
  watch: {
    value: {
      handler(val) {
        this.config = { ...this.config, ...val }
      },
      immediate: true
    },
    config: {
      handler(val) {
        this.$emit('input', val)
      },
      deep: true
    }
  }
}
</script> 