export default function registerLanguage(monaco) {
  monaco.languages.register({ id: "log" });

  monaco.languages.setMonarchTokensProvider("log", {
    tokenizer: {
      root: [
        // 时间戳
        [/\d{4}-\d{2}-\d{2}\s\d{2}:\d{2}:\d{2}/, "date"],
        [/\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}(\.\d+)?Z?/, "date"],

        // 日志级别
        [/\b(TRACE|DEBUG|INFO|WARN|ERROR|FATAL)\b/, "level"],
        [/\[([^\]]+)\]/, "category"],

        // IP地址
        [/\b\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3}\b/, "ip"],

        // 请求方法
        [/\b(GET|POST|PUT|DELETE|PATCH|HEAD|OPTIONS)\b/, "method"],

        // HTTP状态码
        [/\b([1-5]\d{2})\b/, "status"],

        // JSON数据
        [/{[^}]*}/, "json"],
        [/\[[^\]]*\]/, "json"],

        // 异常堆栈
        [/\bat\s+.*\(.*\)/, "stack"],
        [/\s+at\s+.*/, "stack"],

        // 路径
        [/[a-zA-Z]:\\[^\\]+\\[^\\]*/, "path"],
        [/\/[^\s/]+\/[^\s]*/, "path"],

        // 其他
        [/\b([0-9a-fA-F]{8}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{12})\b/, "uuid"],
        [/\b\d+\b/, "number"],
        [/"[^"]*"/, "string"],
        [/'[^']*'/, "string"]
      ]
    }
  });

  monaco.editor.defineTheme("log-theme", {
    base: "vs-dark",
    inherit: true,
    rules: [
      { token: "date", foreground: "B5CEA8" },
      { token: "level", foreground: "569CD6", fontStyle: "bold" },
      { token: "category", foreground: "9CDCFE" },
      { token: "ip", foreground: "4EC9B0" },
      { token: "method", foreground: "C586C0" },
      { token: "status", foreground: "CE9178" },
      { token: "json", foreground: "DCDCAA" },
      { token: "stack", foreground: "D16969" },
      { token: "path", foreground: "4EC9B0" },
      { token: "uuid", foreground: "B5CEA8" },
      { token: "number", foreground: "B5CEA8" },
      { token: "string", foreground: "CE9178" }
    ],
    colors: {
      "editor.foreground": "#D4D4D4",
      "editor.background": "#1E1E1E",
      "editor.selectionBackground": "#ADD6FF26",
      "editor.lineHighlightBackground": "#FFFFFF0F",
      "editorCursor.foreground": "#AEAFAD",
      "editorWhitespace.foreground": "#404040"
    }
  });
} 