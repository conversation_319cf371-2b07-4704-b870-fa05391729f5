import request from '@/utils/request'

// 运行SQL
export function runSql(data) {
  return request({
    url: '/rule/sql/run',
    method: 'post',
    data: data
  })
}

// 停止SQL
export function stopSql(data) {
  return request({
    url: '/rule/sql/stop',
    method: 'post',
    data: data
  })
}

// 获取数据源列表
export function listDataSource() {
  return request({
    url: '/rule/source/list',
    method: 'get'
  })
}
