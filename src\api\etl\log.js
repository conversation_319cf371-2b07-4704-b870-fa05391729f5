import request from '@/utils/request';

// 查询作业日志列表
export function listJobLog(query) {
  return request({
    url: '/etl/job-log/list',
    method: 'get',
    params: query
  });
}

// 获取作业日志详情
export function getJobLog(id) {
  return request({
    url: '/etl/job-log/' + id,
    method: 'get'
  });
}

// 删除作业日志
export function delJobLog(ids) {
  return request({
    url: '/etl/job-log/' + ids,
    method: 'delete'
  });
}

// 导出作业日志
export function exportJobLog(query) {
  return request({
    url: '/etl/job-log/export',
    method: 'get',
    params: query
  });
}

// 下载作业日志文件
export function downloadJobLog(id) {
  return request({
    url: '/etl/job-log/download/' + id,
    method: 'get',
    responseType: 'blob'
  });
}
