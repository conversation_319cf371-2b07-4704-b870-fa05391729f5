<template>
  <div class="simple-code-editor">
    <textarea
      ref="textarea"
      :value="value"
      @input="onInput"
      :placeholder="placeholder"
      :class="{ 'is-readonly': readonly }"
      :readonly="readonly"
      :style="{ height: height }"
      class="editor-textarea"
    ></textarea>
  </div>
</template>

<script>
export default {
  name: 'SimpleCodeEditor',
  props: {
    value: {
      type: String,
      default: ''
    },
    language: {
      type: String,
      default: 'text'
    },
    readonly: {
      type: Boolean,
      default: false
    },
    placeholder: {
      type: String,
      default: '请输入代码'
    },
    height: {
      type: String,
      default: '300px'
    },
    options: {
      type: Object,
      default: () => ({})
    },
    hints: {
      type: Array,
      default: () => []
    }
  },
  mounted() {
    this.$emit('editorDidMount', this.$refs.textarea);
  },
  methods: {
    onInput(event) {
      this.$emit('input', event.target.value);
      this.$emit('change', event.target.value);
    },
    getEditor() {
      return this.$refs.textarea;
    },
    getValue() {
      return this.value;
    },
    setValue(value) {
      this.$emit('input', value);
    },
    focus() {
      this.$refs.textarea.focus();
    }
  }
}
</script>

<style scoped>
.simple-code-editor {
  width: 100%;
  position: relative;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
}

.editor-textarea {
  width: 100%;
  padding: 10px;
  line-height: 1.5;
  resize: vertical;
  color: #333;
  font-family: Consolas, Monaco, 'Andale Mono', 'Ubuntu Mono', monospace;
  font-size: 14px;
  border: none;
  outline: none;
  background-color: #f5f7fa;
  overflow: auto;
}

.editor-textarea:focus {
  background-color: #fff;
}

.is-readonly {
  background-color: #f5f5f5;
  cursor: not-allowed;
}
</style> 