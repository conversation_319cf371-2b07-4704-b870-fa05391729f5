import request from '@/utils/request';

// Debug转换
export function debug(params) {
  return request({
    url: '/etl/trans/debug',
    method: 'post',
    params
  });
}

// 查看转换
export function explore(params) {
  return request({
    url: '/etl/trans/explore',
    method: 'get',
    params
  });
}

// 分析转换对数据库的影响
export function impact(params) {
  return request({
    url: '/etl/trans/impact',
    method: 'post',
    params
  });
}

// 获取转换文件
export function ktr(params) {
  return request({
    url: '/etl/trans/ktr',
    method: 'post',
    data: params
  });
}

// 暂停转换
export function pause(params) {
  return request({
    url: '/etl/trans/pause',
    method: 'post',
    params
  });
}

// 预览转换
export function preview(params) {
  return request({
    url: '/etl/trans/preview',
    method: 'post',
    params
  });
}

// 重放转换
export function replay(params) {
  return request({
    url: '/etl/trans/replay',
    method: 'post',
    params
  });
}

// 获取转换执行日志、状态及结果
export function executeResult(params) {
  return request({
    url: '/etl/trans/result',
    method: 'get',
    params
  });
}

// 执行转换
export function run(params) {
  return request({
    url: '/etl/trans/run',
    method: 'post',
    data: params
  });
}

// 保存转换
export function save(params) {
  return request({
    url: '/etl/trans/save',
    method: 'post',
    data: params
  });
}

// 停止转换
export function stop(params) {
  return request({
    url: '/etl/trans/stop',
    method: 'post',
    params
  });
}

// 校验转换
export function verify(params) {
  return request({
    url: '/etl/trans/verify',
    method: 'post',
    params
  });
}

// 获取输入字段
export function inputFields(params, stepName) {
  return request({
    url: '/etl/trans/input-fields',
    method: 'post',
    data: params,
    params: { stepName }
  });
}

// 获取输出字段
export function outputFields(params, stepName) {
  return request({
    url: '/etl/trans/output-fields',
    method: 'post',
    data: params,
    params: { stepName }
  });
}

// 创建ETL转换
export function createEtlTrans(data) {
  return request({
    url: '/etl/trans/create',
    method: 'post',
    data: data
  });
}

// 更新ETL转换
export function updateEtlTrans(data) {
  return request({
    url: '/etl/trans/update',
    method: 'put',
    data: data
  });
}

// 删除ETL转换
export function deleteEtlTrans(id) {
  return request({
    url: '/etl/trans/delete',
    method: 'delete',
    params: { id }
  });
}

// 获取ETL转换详情
export function getEtlTrans(id) {
  return request({
    url: '/etl/trans/get',
    method: 'get',
    params: { id }
  });
}

// 获取ETL转换列表
export function getEtlTransList(ids) {
  return request({
    url: '/etl/trans/list',
    method: 'get',
    params: { ids }
  });
}

// 获取ETL转换分页
export function getEtlTransPage(query) {
  return request({
    url: '/etl/trans/page',
    method: 'get',
    params: query
  });
}

// 执行ETL转换
export function executeEtlTrans(id) {
  return request({
    url: '/etl/trans/execute',
    method: 'post',
    params: { id }
  });
}

// 停止ETL转换
export function stopEtlTrans(id) {
  return request({
    url: '/etl/trans/stop',
    method: 'post',
    params: { id }
  });
}

// 获取设计器URL
export function getDesignerUrl(id) {
  return request({
    url: '/etl/trans/designer',
    method: 'get',
    params: { id }
  });
}

// 保存设计
export function saveDesign(id) {
  return request({
    url: '/etl/trans/save-design',
    method: 'post',
    params: { id }
  });
}
