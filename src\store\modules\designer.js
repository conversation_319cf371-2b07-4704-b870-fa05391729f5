const state = {
  // 当前编辑的大屏
  currentScreen: null,
  // 选中的组件
  selectedComponent: null,
  // 组件列表
  components: [],
  // 画布配置
  canvas: {
    width: 1920,
    height: 1080,
    scale: 1,
    backgroundColor: '#0d2a42',
    backgroundImage: ''
  },
  // 历史记录
  history: {
    past: [],
    future: []
  },
  // 是否处于预览模式
  isPreview: false
}

const mutations = {
  SET_CURRENT_SCREEN: (state, screen) => {
    state.currentScreen = screen
  },
  SET_SELECTED_COMPONENT: (state, component) => {
    state.selectedComponent = component
  },
  SET_COMPONENTS: (state, components) => {
    state.components = components
  },
  ADD_COMPONENT: (state, component) => {
    state.components.push(component)
  },
  UPDATE_COMPONENT: (state, { index, component }) => {
    state.components.splice(index, 1, component)
  },
  DELETE_COMPONENT: (state, index) => {
    state.components.splice(index, 1)
  },
  SET_CANVAS: (state, canvas) => {
    state.canvas = canvas
  },
  UPDATE_CANVAS: (state, config) => {
    state.canvas = {
      ...state.canvas,
      ...config
    }
  },
  PUSH_HISTORY: (state, snapshot) => {
    state.history.past.push(snapshot)
    state.history.future = []
  },
  UNDO: (state) => {
    if (state.history.past.length > 0) {
      const previous = state.history.past.pop()
      state.history.future.push({
        components: [...state.components],
        canvas: { ...state.canvas }
      })
      state.components = previous.components
      state.canvas = previous.canvas
    }
  },
  REDO: (state) => {
    if (state.history.future.length > 0) {
      const next = state.history.future.pop()
      state.history.past.push({
        components: [...state.components],
        canvas: { ...state.canvas }
      })
      state.components = next.components
      state.canvas = next.canvas
    }
  },
  SET_PREVIEW: (state, isPreview) => {
    state.isPreview = isPreview
  }
}

const actions = {
  // 设置当前大屏
  setCurrentScreen({ commit }, screen) {
    commit('SET_CURRENT_SCREEN', screen)
  },
  // 选中组件
  selectComponent({ commit }, component) {
    commit('SET_SELECTED_COMPONENT', component)
  },
  // 设置组件列表
  setComponents({ commit }, components) {
    commit('SET_COMPONENTS', components)
  },
  // 添加组件
  addComponent({ commit, state }, component) {
    commit('PUSH_HISTORY', {
      components: [...state.components],
      canvas: { ...state.canvas }
    })
    commit('ADD_COMPONENT', component)
  },
  // 更新组件
  updateComponent({ commit, state }, { index, component }) {
    commit('PUSH_HISTORY', {
      components: [...state.components],
      canvas: { ...state.canvas }
    })
    commit('UPDATE_COMPONENT', { index, component })
  },
  // 删除组件
  deleteComponent({ commit, state }, index) {
    commit('PUSH_HISTORY', {
      components: [...state.components],
      canvas: { ...state.canvas }
    })
    commit('DELETE_COMPONENT', index)
  },
  // 设置画布配置
  setCanvas({ commit }, canvas) {
    commit('SET_CANVAS', canvas)
  },
  // 更新画布配置
  updateCanvas({ commit, state }, config) {
    commit('PUSH_HISTORY', {
      components: [...state.components],
      canvas: { ...state.canvas }
    })
    commit('UPDATE_CANVAS', config)
  },
  // 撤销
  undo({ commit }) {
    commit('UNDO')
  },
  // 重做
  redo({ commit }) {
    commit('REDO')
  },
  // 设置预览模式
  setPreview({ commit }, isPreview) {
    commit('SET_PREVIEW', isPreview)
  }
}

const getters = {
  currentScreen: state => state.currentScreen,
  selectedComponent: state => state.selectedComponent,
  components: state => state.components,
  canvas: state => state.canvas,
  canUndo: state => state.history.past.length > 0,
  canRedo: state => state.history.future.length > 0,
  isPreview: state => state.isPreview
}

export default {
  namespaced: true,
  state,
  mutations,
  actions,
  getters
} 