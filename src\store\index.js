import Vue from 'vue'
import Vuex from 'vuex'
import app from './modules/app'
import user from './modules/user'
import report from './modules/report'
import log from './modules/log'
import designer from './modules/designer'
import tagsView from './modules/tagsView'
import permission from './modules/permission'
import settings from './modules/settings'
import oauth2 from './modules/oauth2'
import dict from './modules/dict'
import getters from './getters'

Vue.use(Vuex)

const store = new Vuex.Store({
  modules: {
    app,
    user,
    report,
    log,
    designer,
    tagsView,
    permission,
    settings,
    oauth2,
    dict
  },
  getters
})

export default store
