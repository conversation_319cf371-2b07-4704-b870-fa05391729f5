<template>
  <span>
    <span class="grid-content" v-for="(item, index) in formItemsArr" :key="index">
      <EachForm :item="item" v-model="configData" @eachChange="eachChange"></EachForm>
    </span>
  </span>
</template>

<script>
import EachForm from "./EachForm.vue"

export default {
  name: "ConfigForm",
  components: {
    EachForm
  },
  props: {
    value: {
      type: [Object, String],
      default: () => ({})
    },
    formItems: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      configData: {
        payType: []
      },
      formItemsArr: []
    }
  },
  watch: {
    value: {
      handler(newValue) {
        if (typeof newValue === "string") {
          // 字符串类型表示编辑模式，需要解析JSON
          setTimeout(() => {
            this.configData = JSON.parse(newValue || "{}")
          }, 10)
        } else {
          this.configData = newValue || {}
        }
      },
      immediate: true
    },
    formItems: {
      handler(val) {
        this.formItemsArr = val
      },
      immediate: true
    },
    configData: {
      handler(val) {
        this.$emit('input', val)
      },
      deep: true
    }
  },
  methods: {
    // 子组件值变化时触发
    eachChange(val) {
      this.$emit('change', val)
    },
    // 清空表单数据
    clearForm() {
      let obj = this.configData
      Object.keys(obj).forEach(key => {
        if (obj[key]) {
          if (Array.isArray(obj[key])) {
            obj[key] = []
          } else {
            obj[key] = ""
          }
        }
      })
    },
    // 设置表单数据
    setFormData(data) {
      this.configData = {
        ...this.configData,
        ...data
      }
    },
    // 获取表单数据
    getFormData() {
      return this.configData
    },
    // 验证表单
    validate() {
      return new Promise((resolve, reject) => {
        const isValid = Object.keys(this.configData).every(key => {
          const item = this.formItemsArr.find(item => item.key === key)
          if (item && item.required && !this.configData[key]) {
            return false
          }
          return true
        })
        if (isValid) {
          resolve(this.configData)
        } else {
          reject(new Error('表单验证失败'))
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.grid-content {
  display: inline-block;
  margin-right: 10px;
  margin-bottom: 10px;
}
</style> 