<template>
  <el-button
    :type="type"
    :size="size || 'mini'"
    :icon="icon"
    v-bind="$attrs"
    v-on="$listeners"
  >
    <slot></slot>
  </el-button>
</template>

<script>
export default {
  name: 'RuleButton',
  props: {
    // 按钮类型
    type: {
      type: String,
      default: ''
    },
    // 按钮大小
    size: {
      type: String,
      default: 'mini'
    },
    // 图标类名
    icon: {
      type: String,
      default: ''
    }
  }
}
</script>

<style lang="scss" scoped>
.el-button {
  &.el-button--text {
    padding: 0 4px;
  }
  & + .el-button {
    margin-left: 8px;
  }
}
</style> 