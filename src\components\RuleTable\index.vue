<template>
  <div class="rule-table">
    <el-table
      v-loading="loading"
      :data="data"
      border
      :size="size || 'medium'"
      :height="height || tableHeight"
      v-bind="$attrs"
      v-on="$listeners"
      style="width: 100%; margin: 15px 0;"
    >
      <template v-if="showIndex">
        <el-table-column label="序号" width="55" align="center" fixed>
          <template slot-scope="scope">
            <span>{{ scope.$index + 1 }}</span>
          </template>
        </el-table-column>
      </template>
      <slot></slot>
    </el-table>

    <el-pagination
      v-if="showPagination"
      :current-page.sync="currentPage"
      :page-size.sync="pageSize"
      :total="total"
      :page-sizes="pageSizes || [10, 20, 50, 100]"
      layout="total, sizes, prev, pager, next, jumper"
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
    />
  </div>
</template>

<script>
export default {
  name: 'RuleTable',
  props: {
    // 表格数据
    data: {
      type: Array,
      required: true
    },
    // 加载状态
    loading: {
      type: Boolean,
      default: false
    },
    // 表格大小
    size: {
      type: String,
      default: 'medium'
    },
    // 表格高度
    height: {
      type: [String, Number],
      default: ''
    },
    // 是否显示序号列
    showIndex: {
      type: Boolean,
      default: true
    },
    // 是否显示分页
    showPagination: {
      type: Boolean,
      default: true
    },
    // 分页大小选项
    pageSizes: {
      type: Array,
      default: () => [10, 20, 50, 100]
    },
    // 当前页码
    page: {
      type: Number,
      default: 1
    },
    // 每页条数
    limit: {
      type: Number,
      default: 20
    },
    // 总条数
    total: {
      type: Number,
      default: 0
    }
  },
  data() {
    return {
      tableHeight: document.body.offsetHeight - 310 + 'px',
      currentPage: this.page,
      pageSize: this.limit
    }
  },
  watch: {
    page(val) {
      this.currentPage = val
    },
    limit(val) {
      this.pageSize = val
    }
  },
  methods: {
    handleSizeChange(val) {
      this.$emit('pagination', { page: this.currentPage, limit: val })
    },
    handleCurrentChange(val) {
      this.$emit('pagination', { page: val, limit: this.pageSize })
    }
  }
}
</script>

<style lang="scss" scoped>
.rule-table {
  .el-pagination {
    margin-top: 15px;
    text-align: right;
  }
}
</style> 