import request from '@/utils/request'

// 查询路由配置列表
export function listRoute(query) {
  return request({
    url: '/esb/route/page',
    method: 'get',
    params: query
  })
}

// 查询路由配置详细
export function getRoute(id) {
  return request({
    url: '/esb/route/get?id=' + id,
    method: 'get'
  })
}

// 新增路由配置
export function addRoute(data) {
  return request({
    url: '/esb/route/create',
    method: 'post',
    data: data
  })
}

// 更新路由配置
export function updateRoute(data) {
  return request({
    url: '/esb/route/update',
    method: 'put',
    data: data
  })
}

// 删除路由配置
export function deleteRoute(id) {
  return request({
    url: '/esb/route/delete?id=' + id,
    method: 'delete'
  })
}

// 启用/禁用路由
export function updateRouteStatus(id, status) {
  return request({
    url: '/esb/route/update-status',
    method: 'put',
    data: {
      id,
      status
    }
  })
} 