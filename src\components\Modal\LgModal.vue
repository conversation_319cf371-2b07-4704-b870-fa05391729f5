<template>
  <transition name="modal-fade">
    <div class="modal-backdrop" @click="$emit('closeModal')" v-show="show">
      <div class="modal" :style="{'background': modalBackground}" @click.stop>
        <div class="modal-body" id="modalDescription" :style="{'background': backgroundColor}">
          <slot name="status"><span style="opacity: 0;">{{statusText}}</span></slot>
          <slot name="body"></slot>
          <el-button class="icon-btn_style fr" @click="$emit('closeModal')">
            <i class="el-icon-close"></i>
          </el-button>
        </div>
        <div class="modal-content" v-if="contentText">
          <slot>{{contentText}}</slot>
        </div>
      </div>
    </div>
  </transition>
</template>

<script>
export default {
  name: "LgModal",
  props: {
    show: {
      type: Boolean,
      default: false
    },
    statusText: {
      type: String,
      default: ''
    },
    contentText: {
      type: String,
      default: ''
    },
    backgroundColor: {
      type: String,
      default: '#ffffff'
    },
    modalBackground: {
      type: String,
      default: '#333333'
    }
  },
  methods: {
    close() {
      this.$emit("closeModal");
    }
  }
};
</script>

<style lang="scss" scoped>
.modal-backdrop {
  position: fixed;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  background-color: rgba(0, 0, 0, 0.3);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 2000;

  .modal {
    width: 100vw;
    height: 100vh;
    margin-top: 180px;
    padding-bottom: 50px;
    color: #fff;
    overflow-x: auto;
    display: flex;
    flex-direction: column;
    position: relative;
    border-radius: 0;
    border: 1px solid #333;

    .modal-body {
      width: 100%;
      padding: 20px;
      box-sizing: border-box;
      display: flex;
      justify-content: space-between;
      align-items: center;
      background: #fff;
      color: #333;
    }

    .modal-content {
      width: 100%;
      margin-bottom: 20px;
      text-align: center;
      padding: 20px;
      box-sizing: border-box;
    }
  }
}

.icon-btn_style {
  padding: 5px;
  margin: -5px;
  
  i {
    font-size: 20px;
    color: #909399;
  }
}

/* 动画 */
.modal-fade-enter,
.modal-fade-leave-active {
  opacity: 0;
}

.modal-fade-enter-active,
.modal-fade-leave-active {
  transition: opacity 0.3s ease;
}
</style> 