<template>
  <el-breadcrumb class="app-breadcrumb" separator="/">
    <transition-group name="breadcrumb">
      <el-breadcrumb-item
        v-for="(item, index) in levelListArr"
        :key="item.path"
      >
        <span
          v-if="
            item.redirect === 'noredirect' || index == levelListArr.length - 1
          "
          class="no-redirect"
          >{{ item.meta.title }}</span
        >
        <router-link
          v-else
          :to="item.redirect || item.path"
          class="no-redirect"
          >{{ item.meta.title }}</router-link
        >
      </el-breadcrumb-item>
    </transition-group>
  </el-breadcrumb>
</template>

<script>
import * as pathToRegexp from "path-to-regexp";

export default {
  name: 'Breadcrumb',
  data() {
    return {
      levelList: null
    };
  },
  watch: {
    $route() {
      this.getBreadcrumb();
    }
  },
  computed: {
    levelListArr() {
      if (!this.levelList) return [];
      return this.levelList.filter(item => item.meta && item.meta.title);
    }
  },
  created() {
    this.getBreadcrumb();
  },
  methods: {
    getBreadcrumb() {
      const { params } = this.$route;
      let matched = this.$route.matched.filter(item => {
        if (item.name) {
          // 解决动态路由参数问题
          const toPath = pathToRegexp.compile(item.path);
          item.path = toPath(params);
          return true;
        }
        return false;
      });
      this.levelList = matched;
    }
  }
};
</script>

<style lang="scss" scoped>
.app-breadcrumb.el-breadcrumb {
  display: inline-block;
  font-size: 14px;
  line-height: 50px;
  margin-left: 10px;
  
  .no-redirect {
    color: #333;
    cursor: text;
  }
}

// 面包屑动画
.breadcrumb-enter-active,
.breadcrumb-leave-active {
  transition: all .5s;
}

.breadcrumb-enter,
.breadcrumb-leave-active {
  opacity: 0;
  transform: translateX(20px);
}

.breadcrumb-move {
  transition: all .5s;
}

.breadcrumb-leave-active {
  position: absolute;
}
</style>
