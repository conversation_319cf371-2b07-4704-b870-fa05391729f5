const state = {
  // 访问日志列表
  accessLogs: [],
  // 导出日志列表
  exportLogs: [],
  // 访问统计数据
  statistics: {
    trend: {
      dates: [],
      counts: []
    },
    topReports: {
      reports: [],
      counts: []
    },
    areaDistribution: {
      areas: [],
      counts: []
    },
    deviceDistribution: {
      devices: [],
      counts: []
    }
  },
  // 查询参数
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    dateRange: [],
    reportType: undefined
  }
}

const mutations = {
  SET_ACCESS_LOGS: (state, logs) => {
    state.accessLogs = logs
  },
  SET_EXPORT_LOGS: (state, logs) => {
    state.exportLogs = logs
  },
  SET_STATISTICS: (state, statistics) => {
    state.statistics = statistics
  },
  SET_QUERY_PARAMS: (state, params) => {
    state.queryParams = {
      ...state.queryParams,
      ...params
    }
  }
}

const actions = {
  // 设置访问日志
  setAccessLogs({ commit }, logs) {
    commit('SET_ACCESS_LOGS', logs)
  },
  // 设置导出日志
  setExportLogs({ commit }, logs) {
    commit('SET_EXPORT_LOGS', logs)
  },
  // 设置统计数据
  setStatistics({ commit }, statistics) {
    commit('SET_STATISTICS', statistics)
  },
  // 设置查询参数
  setQueryParams({ commit }, params) {
    commit('SET_QUERY_PARAMS', params)
  }
}

const getters = {
  accessLogs: state => state.accessLogs,
  exportLogs: state => state.exportLogs,
  statistics: state => state.statistics,
  queryParams: state => state.queryParams,
  // 访问趋势数据
  trendData: state => state.statistics.trend,
  // 报表排行数据
  topReportsData: state => state.statistics.topReports,
  // 地区分布数据
  areaData: state => state.statistics.areaDistribution,
  // 设备分布数据
  deviceData: state => state.statistics.deviceDistribution
}

export default {
  namespaced: true,
  state,
  mutations,
  actions,
  getters
} 