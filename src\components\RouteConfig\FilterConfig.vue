<template>
  <div class="filter-config">
    <el-form label-width="120px">
      <!-- 过滤条件配置 -->
      <el-form-item label="过滤规则">
        <div v-for="(rule, index) in config.rules" :key="index" class="rule-item">
          <el-row :gutter="10">
            <el-col :span="7">
              <el-input v-model="rule.field" placeholder="字段名称" />
            </el-col>
            <el-col :span="6">
              <el-select v-model="rule.operator" placeholder="操作符">
                <el-option label="等于" value="eq" />
                <el-option label="不等于" value="ne" />
                <el-option label="大于" value="gt" />
                <el-option label="大于等于" value="ge" />
                <el-option label="小于" value="lt" />
                <el-option label="小于等于" value="le" />
                <el-option label="包含" value="contains" />
                <el-option label="不包含" value="notContains" />
                <el-option label="开始于" value="startsWith" />
                <el-option label="结束于" value="endsWith" />
                <el-option label="为空" value="isNull" />
                <el-option label="不为空" value="isNotNull" />
              </el-select>
            </el-col>
            <el-col :span="7" v-if="!['isNull', 'isNotNull'].includes(rule.operator)">
              <el-input v-model="rule.value" placeholder="比较值" />
            </el-col>
            <el-col :span="2">
              <el-button type="text" icon="el-icon-delete" @click="removeRule(index)" />
            </el-col>
          </el-row>
        </div>
        <el-button type="text" icon="el-icon-plus" @click="addRule">添加规则</el-button>
      </el-form-item>

      <!-- 规则组合方式 -->
      <el-form-item label="组合方式">
        <el-radio-group v-model="config.combineType">
          <el-radio label="and">满足所有条件</el-radio>
          <el-radio label="or">满足任一条件</el-radio>
        </el-radio-group>
      </el-form-item>

      <!-- 高级配置 -->
      <el-form-item label="高级配置">
        <el-collapse>
          <el-collapse-item title="自定义过滤表达式">
            <el-input
              type="textarea"
              v-model="config.customExpression"
              :rows="4"
              placeholder="请输入自定义过滤表达式，例如：${body.price} > 100 && ${body.category} == 'electronics'"
            />
            <div class="expression-help">
              <el-tooltip placement="top">
                <div slot="content">
                  支持的表达式语法：<br/>
                  - ${body.field} 访问消息体字段<br/>
                  - ${header.field} 访问消息头字段<br/>
                  - 支持基本运算符：+, -, *, /, %, ==, !=, >, <, >=, <=<br/>
                  - 支持逻辑运算符：&&, ||, !<br/>
                  - 支持字符串函数：contains(), startsWith(), endsWith()
                </div>
                <el-link type="info" icon="el-icon-question">表达式语法帮助</el-link>
              </el-tooltip>
            </div>
          </el-collapse-item>
        </el-collapse>
      </el-form-item>

      <!-- 异常处理 -->
      <el-form-item label="异常处理">
        <el-select v-model="config.errorHandler">
          <el-option label="忽略并继续" value="ignore" />
          <el-option label="停止处理" value="stop" />
          <el-option label="重定向到错误处理器" value="redirect" />
        </el-select>
      </el-form-item>
    </el-form>
  </div>
</template>

<script>
export default {
  name: 'FilterConfig',
  props: {
    value: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      config: {
        rules: [],
        combineType: 'and',
        customExpression: '',
        errorHandler: 'ignore'
      }
    }
  },
  watch: {
    value: {
      handler(val) {
        this.config = { ...this.config, ...val }
      },
      immediate: true
    },
    config: {
      handler(val) {
        this.$emit('input', val)
      },
      deep: true
    }
  },
  methods: {
    addRule() {
      this.config.rules.push({
        field: '',
        operator: 'eq',
        value: ''
      })
    },
    removeRule(index) {
      this.config.rules.splice(index, 1)
    }
  }
}
</script>

<style scoped>
.rule-item {
  margin-bottom: 15px;
  padding: 10px;
  border: 1px solid #EBEEF5;
  border-radius: 4px;
}
.expression-help {
  margin-top: 8px;
  font-size: 12px;
}
</style> 