import request from '@/config/axios'

export interface OAuth2AppVO {
  id: string
  name: string
  description?: string
  icon?: string
  url?: string
  status: number
}

// 获取OAuth2应用列表
export const getOAuth2AppsApi = () => {
  return request.get<OAuth2AppVO[]>({ url: '/system/oauth2-app/list' })
}

// 获取用户可访问的OAuth2应用
export const getUserAccessibleAppsApi = () => {
  return request.get<OAuth2AppVO[]>({ url: '/system/oauth2-app/accessible' })
}