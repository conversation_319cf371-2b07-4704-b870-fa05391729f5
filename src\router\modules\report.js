/** 报表设计器路由 */
import Layout from '@/layout'

const reportRouter = {
  path: '/report',
  component: Layout,
  redirect: 'noRedirect',
  name: 'Report',
  meta: {
    title: '报表设计',
    icon: 'chart',
    permissions: ['report:view']
  },
  children: [
    {
      path: 'datasource',
      component: () => import('@/views/report/datasource/index'),
      name: 'DataSource',
      meta: { 
        title: '数据源管理',
        icon: 'database',
        permissions: ['report:datasource:list']
      }
    },
    {
      path: 'design',
      component: () => import('@/views/report/design/index'),
      name: 'ReportDesign',
      meta: { 
        title: '报表设计',
        permissions: ['report:design:list']
      }
    },
    {
      path: 'designer',
      component: () => import('@/views/report/designer/index'),
      name: 'ReportDesigner',
      meta: { 
        title: '设计器', 
        activeMenu: '/report/design',
        permissions: ['report:design:edit']
      },
      hidden: true
    },
    {
      path: 'preview/:id',
      component: () => import('@/views/report/preview/index'),
      name: 'ReportPreview',
      meta: { 
        title: '报表预览', 
        activeMenu: '/report/design',
        permissions: ['report:design:view']
      },
      hidden: true
    },
    {
      path: 'bi',
      component: { render: (e) => e("router-view") },
      redirect: '/report/bi/dashboard',
      name: 'ReportBI',
      meta: {
        title: 'BI报表',
        icon: 'chart',
        permissions: ['report:bi:view']
      },
      children: [
        {
          path: 'dashboard',
          component: () => import('@/views/report/bi/dashboard/index'),
          name: 'BIDashboard',
          meta: { 
            title: '仪表盘', 
            icon: 'dashboard', 
            permissions: ['report:bi:dashboard:list']
          }
        },
        {
          path: 'dataset',
          component: () => import('@/views/report/bi/dataset/index'),
          name: 'BIDataset',
          meta: { 
            title: '数据集管理', 
            icon: 'table',
            permissions: ['report:bi:dataset:list']
          }
        },
        {
          path: 'designer',
          component: () => import('@/views/report/bi/designer/index'),
          name: 'BIDesigner',
          meta: { 
            title: '设计器', 
            icon: 'edit', 
            permissions: ['report:bi:dashboard:edit']
          }
        },
        {
          path: 'preview/:id',
          component: () => import('@/views/report/bi/preview/index'),
          name: 'BIPreview',
          meta: { 
            title: '预览', 
            icon: 'eye', 
            activeMenu: '/report/bi/dashboard',
            permissions: ['report:bi:dashboard:view']
          },
          hidden: true
        }
      ]
    }
  ]
}

export default reportRouter 