import request from '@/utils/request'

// 查询作业调度列表
export function getJobSchedulePage(query) {
  return request({
    url: '/etl/job-schedule/page',
    method: 'get',
    params: query
  })
}

// 查询作业调度详情
export function getJobSchedule(id) {
  return request({
    url: '/etl/job-schedule/get',
    method: 'get',
    params: { id }
  })
}

// 新增作业调度
export function createJobSchedule(data) {
  return request({
    url: '/etl/job-schedule/create',
    method: 'post',
    data: data
  })
}

// 修改作业调度
export function updateJobSchedule(data) {
  return request({
    url: '/etl/job-schedule/update',
    method: 'put',
    data: data
  })
}

// 删除作业调度
export function deleteJobSchedule(id) {
  return request({
    url: '/etl/job-schedule/delete',
    method: 'delete',
    params: { id }
  })
}

// 执行作业调度
export function executeJobSchedule(id) {
  return request({
    url: '/etl/job-schedule/execute',
    method: 'post',
    params: { id }
  })
}

// 启动作业调度
export function startJobSchedule(id) {
  return request({
    url: '/etl/job-schedule/start',
    method: 'put',
    params: { id }
  })
}

// 暂停作业调度
export function pauseJobSchedule(id) {
  return request({
    url: '/etl/job-schedule/pause',
    method: 'put',
    params: { id }
  })
}

// 导出作业调度
export function exportJobSchedule(query) {
  return request({
    url: '/etl/job-schedule/export',
    method: 'get',
    params: query,
    responseType: 'blob'
  })
}

// 获取作业简单列表
export function getSimpleJobList() {
  return request({
    url: '/etl/job/list-all-simple',
    method: 'get'
  })
} 