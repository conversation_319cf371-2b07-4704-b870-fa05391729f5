import { debounce } from 'throttle-debounce'

export default {
  data() {
    return {
      $_sidebarElm: null,
      $_resizeHandler: null
    }
  },
  mounted() {
    this.initListener()
  },
  activated() {
    if (!this.$_resizeHandler) {
      // avoid duplication init
      this.initListener()
    }

    // 当keep-alive激活组件时，更新图表
    this.$_resizeHandler()
  },
  beforeD<PERSON>roy() {
    this.destroyListener()
  },
  deactivated() {
    this.destroyListener()
  },
  methods: {
    // 使用防抖函数创建resize处理器
    $_initResizeEvent() {
      window.addEventListener('resize', this.$_resizeHandler)
    },
    $_destroyResizeEvent() {
      window.removeEventListener('resize', this.$_resizeHandler)
    },
    // 侦听侧边栏改变事件
    $_sidebarResizeEvent() {
      this.$_sidebarElm = document.getElementsByClassName('sidebar-container')[0]
      if (this.$_sidebarElm) {
        this.$_sidebarElm.addEventListener('transitionend', this.$_resizeHandler)
      }
    },
    $_destroySidebarEvent() {
      if (this.$_sidebarElm) {
        this.$_sidebarElm.removeEventListener('transitionend', this.$_resizeHandler)
      }
    },
    initListener() {
      // 使用防抖处理resize事件，避免频繁触发
      this.$_resizeHandler = debounce(100, () => {
        if (this.chart) {
          // 如果组件实现了resize方法，则调用
          this.chart.resize()
        }
      })
      this.$_initResizeEvent()
      this.$_sidebarResizeEvent()
    },
    destroyListener() {
      this.$_destroyResizeEvent()
      this.$_destroySidebarEvent()
      this.$_resizeHandler = null
    }
  }
} 