import request from '@/utils/request'

// 查询变更记录列表
export function pageChangeRecord(query) {
  return request({
    url: '/metadata/record/page',
    method: 'get',
    params: query
  })
}

// 查询变更记录详细
export function getChangeRecord(id) {
  return request({
    url: '/metadata/record/' + id,
    method: 'get'
  })
}

// 新增变更记录
export function addChangeRecord(data) {
  return request({
    url: '/metadata/record',
    method: 'post',
    data: data
  })
}

// 修改变更记录
export function updateChangeRecord(data) {
  return request({
    url: '/metadata/record',
    method: 'put',
    data: data
  })
}

// 删除变更记录
export function delChangeRecord(id) {
  return request({
    url: '/metadata/record/' + id,
    method: 'delete'
  })
}
