import request from '@/utils/request'

export function engineMode() {
  return request({
    url: '/etl/engine/mode',
    method: 'get',
    loading: true
  })
}

export function engineInfo() {
  return request({
    url: '/etl/engine/info',
    method: 'get',
    loading: true
  })
}

export function validate(data) {
  return request({
    url: '/engine/validate',
    method: 'post',
    loading: true,
    data
  })
}

export function save(data) {
  return request({
    url: '/engine/save',
    method: 'post',
    loading: true,
    data
  })
}

export function dbPreview(data) {
  return request({
    url: '/etl/dataset/table/dbPreview',
    method: 'post',
    loading: true,
    data
  })
}
