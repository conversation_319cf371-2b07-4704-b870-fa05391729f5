/**
 * 操作权限处理
 * Copyright (c) 2019 ruoyi
 */
import store from '@/store'
import { hasPermission, hasReportPermission, hasReportCategoryPermission } from '@/utils/permission'

export default {
  inserted(el, binding, vnode) {
    const { value, arg } = binding
    let hasAuth = false

    try {
      // 根据指令参数判断权限类型
      switch (arg) {
        case 'report':
          // v-hasPermi:report="{ reportId: 'xxx', action: 'view' }"
          if (!value || !value.reportId || !value.action) {
            console.error('使用报表权限指令时需要提供reportId和action')
            hasAuth = false
            break
          }
          const { reportId, action } = value
          hasAuth = hasReportPermission(reportId, action)
          break
        case 'category':
          // v-hasPermi:category="{ categoryId: 'xxx', action: 'view' }"
          if (!value || !value.categoryId || !value.action) {
            console.error('使用分类权限指令时需要提供categoryId和action')
            hasAuth = false
            break
          }
          const { categoryId, categoryAction } = value
          hasAuth = hasReportCategoryPermission(categoryId, categoryAction)
          break
        default:
          // v-hasPermi="['system:user:add','system:user:edit']"
          if (!value) {
            console.error('使用权限指令时需要提供权限标识')
            hasAuth = false
            break
          }
          if (typeof value === 'string') {
            hasAuth = hasPermission([value])
          } else if (Array.isArray(value)) {
            hasAuth = hasPermission(value)
          } else {
            console.error('权限标识格式不正确')
            hasAuth = false
          }
      }
    } catch (error) {
      console.error('权限检查出错：', error)
      hasAuth = false
    }

    if (!hasAuth) {
      el.parentNode && el.parentNode.removeChild(el)
    }
  }
}
