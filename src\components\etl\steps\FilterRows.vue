<template>
  <div class="filter-rows-config">
    <el-form label-position="top" size="small">
      <el-form-item label="条件">
        <div class="condition-builder">
          <div class="condition-group">
            <div
              v-for="(condition, index) in config.conditions"
              :key="index"
              class="condition-row">
              <el-select v-model="condition.field" placeholder="选择字段">
                <el-option
                  v-for="field in inputFields"
                  :key="field.name"
                  :label="field.name"
                  :value="field.name"
                />
              </el-select>
              
              <el-select v-model="condition.operator" placeholder="运算符">
                <el-option
                  v-for="op in operators"
                  :key="op.value"
                  :label="op.label"
                  :value="op.value"
                />
              </el-select>
              
              <el-input
                v-model="condition.value"
                placeholder="值"
                :type="getValueType(condition.field)"
              />
              
              <el-select
                v-if="index < config.conditions.length - 1"
                v-model="condition.connector"
                style="width: 80px">
                <el-option label="AND" value="AND" />
                <el-option label="OR" value="OR" />
              </el-select>
              
              <el-button
                type="text"
                icon="el-icon-delete"
                @click="removeCondition(index)"
              />
            </div>
            
            <el-button
              type="text"
              icon="el-icon-plus"
              @click="addCondition">
              添加条件
            </el-button>
          </div>
        </div>
      </el-form-item>

      <el-form-item>
        <el-checkbox v-model="config.sendTrueTo">发送'True'数据到下一步</el-checkbox>
      </el-form-item>

      <el-form-item v-if="!config.sendTrueTo">
        <el-checkbox v-model="config.sendFalseTo">发送'False'数据到下一步</el-checkbox>
      </el-form-item>
    </el-form>
  </div>
</template>

<script>
export default {
  name: 'FilterRows',
  props: {
    value: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      config: {
        conditions: [],
        sendTrueTo: true,
        sendFalseTo: false
      },
      inputFields: [],
      operators: [
        { label: '等于', value: '=' },
        { label: '不等于', value: '!=' },
        { label: '大于', value: '>' },
        { label: '大于等于', value: '>=' },
        { label: '小于', value: '<' },
        { label: '小于等于', value: '<=' },
        { label: '包含', value: 'CONTAINS' },
        { label: '开始于', value: 'STARTS WITH' },
        { label: '结束于', value: 'ENDS WITH' },
        { label: '为空', value: 'IS NULL' },
        { label: '不为空', value: 'IS NOT NULL' },
        { label: '在列表中', value: 'IN' },
        { label: '不在列表中', value: 'NOT IN' },
        { label: '正则匹配', value: 'REGEXP' }
      ]
    }
  },
  watch: {
    value: {
      handler(val) {
        this.config = { ...this.config, ...val }
      },
      immediate: true,
      deep: true
    },
    config: {
      handler(val) {
        this.$emit('input', val)
      },
      deep: true
    }
  },
  methods: {
    addCondition() {
      this.config.conditions.push({
        field: '',
        operator: '=',
        value: '',
        connector: 'AND'
      })
    },
    removeCondition(index) {
      this.config.conditions.splice(index, 1)
    },
    getValueType(fieldName) {
      // 根据字段类型返回对应的输入类型
      const field = this.inputFields.find(f => f.name === fieldName)
      if (!field) return 'text'
      
      switch (field.type) {
        case 'number':
          return 'number'
        case 'date':
          return 'date'
        case 'datetime':
          return 'datetime-local'
        default:
          return 'text'
      }
    }
  },
  async created() {
    try {
      // TODO: 获取输入字段列表
    } catch (error) {
      console.error('获取字段失败：', error)
    }
  }
}
</script>

<style lang="scss" scoped>
.filter-rows-config {
  padding: 16px;

  .condition-builder {
    border: 1px solid #dcdfe6;
    border-radius: 4px;
    padding: 16px;

    .condition-group {
      .condition-row {
        display: flex;
        align-items: center;
        margin-bottom: 8px;
        gap: 8px;

        .el-select {
          min-width: 120px;
        }

        .el-input {
          flex: 1;
        }
      }
    }
  }
}
</style>
