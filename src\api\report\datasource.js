import request from '@/utils/request'

// 测试SQL
export function testSql(data) {
  return request({
    url: '/report/datasource/test-sql',
    method: 'post',
    data
  })
}

// 测试API
export function testApi(data) {
  return request({
    url: '/report/datasource/test-api',
    method: 'post',
    data
  })
}

// 获取数据库列表
export function listDatabase() {
  return request({
    url: '/report/datasource/database',
    method: 'get'
  })
}

// 获取数据源列表
export function listDatasource(query) {
  return request({
    url: '/report/datasource/list',
    method: 'get',
    params: query
  })
}

// 获取数据源详情
export function getDatasource(id) {
  return request({
    url: `/report/datasource/${id}`,
    method: 'get'
  })
}

// 新增数据源
export function addDatasource(data) {
  return request({
    url: '/report/datasource',
    method: 'post',
    data
  })
}

// 修改数据源
export function updateDatasource(data) {
  return request({
    url: '/report/datasource',
    method: 'put',
    data
  })
}

// 删除数据源
export function deleteDatasource(id) {
  return request({
    url: `/report/datasource/${id}`,
    method: 'delete'
  })
}

// 测试数据源连接
export function testConnection(data) {
  return request({
    url: '/report/datasource/test-connection',
    method: 'post',
    data
  })
}

// 获取数据源字段
export function getFields(id) {
  return request({
    url: `/report/datasource/${id}/fields`,
    method: 'get'
  })
}

// 获取数据源数据
export function getData(id, params) {
  return request({
    url: `/report/datasource/${id}/data`,
    method: 'get',
    params
  })
}

// 获取数据库表列表
export function listTables(id) {
  return request({
    url: `/report/datasource/${id}/tables`,
    method: 'get'
  })
}

// 获取表字段列表
export function listColumns(id, tableName) {
  return request({
    url: `/report/datasource/${id}/columns`,
    method: 'get',
    params: { tableName }
  })
}

// 执行SQL查询
export function executeQuery(id, data) {
  return request({
    url: `/report/datasource/${id}/query`,
    method: 'post',
    data
  })
} 