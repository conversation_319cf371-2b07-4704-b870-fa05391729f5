/**
 * 公式计算工具类
 */

// 支持的运算符
const OPERATORS = {
  '+': (a, b) => a + b,
  '-': (a, b) => a - b,
  '*': (a, b) => a * b,
  '/': (a, b) => b !== 0 ? a / b : 0,
  '%': (a, b) => b !== 0 ? a % b : 0
}

// 支持的聚合函数
const AGGREGATES = {
  'SUM': (values) => values.reduce((sum, val) => sum + (Number(val) || 0), 0),
  'AVG': (values) => {
    const sum = values.reduce((sum, val) => sum + (Number(val) || 0), 0)
    return values.length > 0 ? sum / values.length : 0
  },
  'COUNT': (values) => values.filter(val => val != null && val !== '').length,
  'MAX': (values) => {
    const nums = values.map(val => Number(val)).filter(val => !isNaN(val))
    return nums.length > 0 ? Math.max(...nums) : null
  },
  'MIN': (values) => {
    const nums = values.map(val => Number(val)).filter(val => !isNaN(val))
    return nums.length > 0 ? Math.min(...nums) : null
  }
}

/**
 * 解析公式
 * @param {string} formula 公式字符串
 * @returns {object} 解析结果
 */
export function parseFormula(formula) {
  try {
    // 移除空格
    formula = formula.replace(/\s/g, '')
    
    // 检查是否是聚合函数
    const aggregateMatch = formula.match(/^(\w+)\((.*)\)$/)
    if (aggregateMatch) {
      const [_, func, args] = aggregateMatch
      if (AGGREGATES[func]) {
        return {
          type: 'aggregate',
          function: func,
          arguments: args.split(',').map(arg => arg.trim())
        }
      }
    }
    
    // 解析算术表达式
    let tokens = []
    let currentToken = ''
    
    for (let i = 0; i < formula.length; i++) {
      const char = formula[i]
      if (OPERATORS[char]) {
        if (currentToken) {
          tokens.push(currentToken)
          currentToken = ''
        }
        tokens.push(char)
      } else {
        currentToken += char
      }
    }
    if (currentToken) {
      tokens.push(currentToken)
    }
    
    return {
      type: 'arithmetic',
      tokens
    }
  } catch (error) {
    console.error('公式解析失败', error)
    return null
  }
}

/**
 * 计算公式结果
 * @param {object} parsedFormula 解析后的公式
 * @param {object} context 计算上下文
 * @returns {number} 计算结果
 */
export function calculateFormula(parsedFormula, context) {
  try {
    if (!parsedFormula) {
      return null
    }

    const { type } = parsedFormula
    
    if (type === 'aggregate') {
      const { function: func, arguments: args } = parsedFormula
      // 获取参数值
      const values = args.map(arg => {
        if (arg.startsWith('${') && arg.endsWith('}')) {
          const field = arg.slice(2, -1)
          return context.getFieldValues?.(field) || []
        }
        return [Number(arg) || 0]
      }).flat()
      
      // 执行聚合计算
      return AGGREGATES[func](values)
    }
    
    if (type === 'arithmetic') {
      const { tokens } = parsedFormula
      // 计算算术表达式
      let result = getValue(tokens[0], context)
      
      for (let i = 1; i < tokens.length; i += 2) {
        const operator = tokens[i]
        const value = getValue(tokens[i + 1], context)
        result = OPERATORS[operator](result, value)
      }
      
      return result
    }
    
    return null
  } catch (error) {
    console.error('公式计算失败', error)
    return null
  }
}

/**
 * 获取值
 * @param {string} token 标记
 * @param {object} context 上下文
 * @returns {number} 值
 */
function getValue(token, context) {
  if (token.startsWith('${') && token.endsWith('}')) {
    const field = token.slice(2, -1)
    return Number(context.getFieldValue?.(field)) || 0
  }
  return Number(token) || 0
} 