import request from '@/utils/request'

// 获取访问日志列表
export function listAccessLog(query) {
  return request({
    url: '/report/log/access/list',
    method: 'get',
    params: query
  })
}

// 获取访问日志详情
export function getAccessLog(id) {
  return request({
    url: `/report/log/access/${id}`,
    method: 'get'
  })
}

// 导出访问日志
export function exportAccessLog(query) {
  return request({
    url: '/report/log/access/export',
    method: 'get',
    params: query,
    responseType: 'blob'
  })
}

// 获取统计数据
export function getStatistics(query) {
  return request({
    url: '/report/log/statistics',
    method: 'get',
    params: query
  })
}

// 获取导出记录列表
export function listExportLog(query) {
  return request({
    url: '/report/log/export/list',
    method: 'get',
    params: query
  })
}

// 获取导出记录详情
export function getExportLog(id) {
  return request({
    url: `/report/log/export/${id}`,
    method: 'get'
  })
}

// 删除导出记录
export function deleteExportLog(id) {
  return request({
    url: `/report/log/export/${id}`,
    method: 'delete'
  })
}

// 批量删除导出记录
export function deleteExportLogBatch(ids) {
  return request({
    url: '/report/log/export/batch',
    method: 'delete',
    data: ids
  })
}

// 下载导出文件
export function downloadExportFile(id) {
  return request({
    url: `/report/log/export/${id}/download`,
    method: 'get',
    responseType: 'blob'
  })
} 