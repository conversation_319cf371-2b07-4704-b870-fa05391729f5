export default {
  fu: {
    search_bar: {
      search: '搜索',
      adv_search: '高級搜索',
      ok: '确定',
      cancel: '取消',
      please_select: '请选择',
      please_input: '请输入',
      like: '包含',
      not_like: '不包含',
      in: '属于',
      not_in: '不屬于',
      gt: '大于',
      ge: '大于等于',
      lt: '小于',
      le: '小于等于',
      eq: '等于',
      ne: '不等于',
      between: '之间',
      select_date: '选择日期',
      start_date: '开始日期',
      end_date: '結束日期',
      select_date_time: '选择日期时间',
      start_date_time: '开始日期时间',
      end_date_time: '結束日期时间',
      range_separator: '至',
      data_time_error: '开始日期不能大于結束日期',
      clean: '清空',
      refresh: '刷新'
    },
    table: {
      ok: '确定',
      custom_table_fields: '自定义表格字段',
      custom_table_fields_desc: '固定字段不在选择范围'
    },
    steps: {
      cancel: '取消',
      next: '下一步',
      prev: '上一步',
      finish: '完成'
    }
  },
  track: {
    upload_limit_format: '图片格式错误，支持JPG，PNG',
    upload_limit_size: '图片大小不超过'
  },
  route: {
    dashboard: '首页',
    documentation: '文档',
    guide: '引导页',
    permission: '权限测试页',
    rolePermission: '角色权限',
    pagePermission: '页面权限',
    directivePermission: '指令权限',
    icons: '图标',
    components: '组件',
    tinymce: '富文本编辑器',
    markdown: 'Markdown',
    jsonEditor: 'JSON 编辑器',
    dndList: '列表拖拽',
    splitPane: 'Splitpane',
    avatarUpload: '头像上传',
    dropzone: 'Dropzone',
    sticky: 'Sticky',
    countTo: 'Count To',
    componentMixin: '小组件',
    backToTop: '返回顶部',
    dragDialog: '拖拽 Dialog',
    dragSelect: '拖拽 Select',
    dragKanban: '可拖拽看板',
    charts: '图表',
    keyboardChart: '键盘图表',
    lineChart: '折线图',
    mixChart: '混合图表',
    example: '综合实例',
    nested: '路由嵌套',
    menu1: '菜单1',
    'menu1-1': '菜单 1-1',
    'menu1-2': '菜单 1-2',
    'menu1-2-1': '菜单 1-2-1',
    'menu1-2-2': '菜单 1-2-2',
    'menu1-3': '菜单 1-3',
    menu2: '菜单 2',
    Table: 'Table',
    dynamicTable: '动态 Table',
    dragTable: '拖拽 Table',
    inlineEditTable: 'Table 内编辑',
    complexTable: '综合 Table',
    tab: 'Tab',
    form: '表单',
    createArticle: '创建文章',
    editArticle: '编辑文章',
    articleList: '文章列表',
    errorPages: '错误页面',
    page401: '401',
    page404: '404',
    errorLog: '错误日志',
    excel: 'Excel',
    exportExcel: '导出 Excel',
    selectExcel: '导出 已选择项',
    mergeHeader: '导出 多级表头',
    uploadExcel: '上传 Excel',
    zip: 'Zip',
    pdf: 'PDF',
    exportZip: 'Export Zip',
    theme: '换肤',
    clipboardDemo: 'Clipboard',
    i18n: '国际化',
    externalLink: '外链',
    profile: '个人中心'
  },
  navbar: {
    dashboard: '首页',
    github: '项目地址',
    logOut: '退出登录',
    profile: '个人中心',
    theme: '换肤',
    size: '布局大小'
  },
  login: {
    title: '系统登录',
    welcome: '欢迎使用',
    logIn: '登录',
    username: '账号',
    password: '密码',
    any: '随便填',
    thirdparty: '第三方登录',
    thirdpartyTips: '本地不能模拟，请结合自己业务进行模拟！！！',
    expires: '登录信息过期，请重新登录',
    tokenError: '登录信息错误，请重新登录',
    username_error: '请输入正确的 ID',
    password_error: '密码不小于 8 位',
    re_login: '重新登录',
    default_login: '普通登录'
  },
  commons: {
    consanguinity: '血缘关系',
    collapse_navigation: '收起导航',
    operate_cancelled: '已取消操作',
    bind: '绑定',
    unbind: '解绑',
    unlock: '解锁',
    unlock_success: '解锁成功',
    uninstall: '卸载',
    parameter_effect: '参数值仅在数据集编辑时生效',
    no_result: '没有找到相关内容',
    manage_member: '管理成员',
    confirm_remove_cancel: '确定删除该角色吗?',
    user_confirm_remove_cancel: '确定将该用户从角色中移除吗?',
    default_value: '默认值',
    params_value: '参数值',
    input_role_name: '请输入角色名称',
    publish: '发布',
    unpublished: '取消发布',
    default_pwd: '初始密码',
    stop: '停止',
    first_login_tips: '您使用的是初始密码，记得修改密码哦',
    roger_that: '知道了',
    donot_noti: '不再提示',
    apply: '应用',
    search: '搜索',
    folder: '目录',
    no_target_permission: '没有权限',
    success: '成功',
    switch_lang: '切换语言成功',
    close: '关闭',
    icon: '图标',
    all: '全部',
    enable: '启用',
    disable: '禁用',
    yes: '是',
    no: '否',
    reset: '重置',
    catalogue: '目录',
    button: '按钮',
    gender: '性别',
    man: '男',
    woman: '女',
    keep_secret: '保密',
    nick_name: '姓名',
    confirmPassword: '确认密码',
    upload: '上传',
    cover: '覆盖',
    not_cover: '不覆盖',
    import_mode: '导入模式',
    import_module: '导入模块',
    please_fill_in_the_template: '请填写模版内容',
    cut_back_old_version: '切回旧版',
    cut_back_new_version: '切回新版',
    comment: '评论',
    examples: '示例',
    help_documentation: '帮助文档',
    api_help_documentation: 'API文档',
    delete_cancelled: '已取消删除',
    workspace: '工作空间',
    organization: '组织',
    menu: '菜单',
    setting: '设置',
    project: '项目',
    about_us: '关于',
    current_project: '当前项目',
    name: '名称',
    description: '描述',
    annotation: '注释',
    clear: '清空',
    save: '保存',
    otherSave: '另存为',
    update: '更新',
    save_success: '保存成功',
    delete_success: '删除成功',
    copy_success: '复制成功',
    modify_success: '修改成功',
    delete_cancel: '已取消删除',
    confirm: '确定',
    cancel: '取消',
    prompt: '提示',
    operating: '操作',
    input_limit: '长度在 {0} 到 {1} 个字符',
    login: '登录',
    welcome: '一站式开源数据分析平台',
    username: '姓名',
    password: '密码',
    input_username: '请输入用户姓名',
    input_password: '请输入密码',
    test: '测试',
    create_time: '创建时间',
    update_time: '更新时间',
    add: '添加',
    member: '成员',
    email: '邮箱',
    phone: '电话',
    mobile_phone: '请输入手机号',
    mobile_phone_number: '手机号码',
    role: '角色',
    personal_info: '个人信息',
    api_keys: 'API Keys',
    quota: '配额管理',
    status: '状态',
    show_all: '显示全部',
    show: '显示',
    report: '报告',
    user: '用户',
    system: '系统',
    personal_setting: '个人设置',
    test_resource_pool: '测试资源池',
    system_setting: '系统设置',
    input_content: '请输入内容',
    create: '新建',
    edit: '编辑',
    copy: '复制',
    refresh: '刷新',
    remark: '备注',
    delete: '删除',
    reduction: '恢复',
    not_filled: '未填写',
    please_select: '请选择',
    search_by_name: '根据名称搜索',
    personal_information: '个人信息',
    exit_system: '退出系统',
    verification: '验证',
    title: '标题',
    custom: '自定义',
    select_date: '选择日期',
    months_1: '一月',
    months_2: '二月',
    months_3: '三月',
    months_4: '四月',
    months_5: '五月',
    months_6: '六月',
    months_7: '七月',
    months_8: '八月',
    months_9: '九月',
    months_10: '十月',
    months_11: '十一月',
    months_12: '十二月',
    weeks_0: '周日',
    weeks_1: '周一',
    weeks_2: '周二',
    weeks_3: '周三',
    weeks_4: '周四',
    weeks_5: '周五',
    weeks_6: '周六',
    system_parameter_setting: '系统参数设置',
    connection_successful: '连接成功',
    connection_failed: '连接失败',
    save_failed: '保存失败',
    host_cannot_be_empty: '主机不能为空',
    port_cannot_be_empty: '端口号不能为空',
    account_cannot_be_empty: '帐户不能为空',
    remove: '移除',
    remove_cancel: '移除取消',
    remove_success: '移除成功',
    tips: '认证信息已过期，请重新登录',
    not_performed_yet: '尚未执行',
    incorrect_input: '输入内容不正确',
    delete_confirm: '请输入以下内容，确认删除：',
    login_username: 'ID 或 邮箱',
    input_login_username: '请输入用户 ID 或 邮箱',
    input_name: '请输入名称',
    please_upload: '请上传文件',
    please_fill_path: '请填写ur路径',
    formatErr: '格式错误',
    please_save: '请先保存',
    reference_documentation: '参考文档',
    id: 'ID',
    millisecond: '毫秒',
    cannot_be_null: '不能为空',
    required: '必填',
    already_exists: '名称不能重复',
    modifier: '修改人',
    validate: '校验',
    batch_add: '批量添加',
    tag_tip: '输入回车添加标签',
    search_keywords: '输入关键字搜索',
    table: {
      select_tip: '已选中 {0} 条数据'
    },
    date: {
      select_date: '选择日期',
      start_date: '开始日期',
      end_date: '结束日期',
      select_date_time: '选择日期时间',
      start_date_time: '开始日期时间',
      end_date_time: '结束日期时间',
      range_separator: '至',
      data_time_error: '开始日期不能大于结束日期',
      one_day: '一天',
      one_week: '一周',
      one_month: '一个月'
    },
    adv_search: {
      title: '高级搜索',
      combine: '组合查询',
      test: '所属测试',
      project: '所属项目',
      search: '查询',
      reset: '重置',
      and: '所有',
      or: '任意一个',
      operators: {
        is_empty: '空',
        is_not_empty: '非空',
        like: '包含',
        not_like: '不包含',
        in: '属于',
        not_in: '不属于',
        gt: '大于',
        ge: '大于等于',
        lt: '小于',
        le: '小于等于',
        equals: '等于',
        not_equals: '不等于',
        between: '之间',
        current_user: '是当前用户'
      },
      message_box: {
        alert: '警告',
        confirm: '确认'
      }
    },
    monitor: '监控',
    image: '镜像',
    tag: '标签',
    module: {
      select_module: '选择模块',
      default_module: '默认模块'
    },
    datasource: '数据源',
    char_can_not_more_50: '不能超过50字符',
    share_success: '分享成功',
    input_id: '请输入ID',
    input_pwd: '请输入密码',
    message_box: {
      alert: '警告',
      confirm: '确认',
      ok: '确认',
      cancel: '取消'
    },
    ukey_title: 'API Keys',
    thumbnail: '缩略图',
    confirm_delete: '确认删除',
    delete_this_dashboard: '确认删除该仪表板吗?',
    confirm_stop: '确认停止',
    stop_success: '停止成功',
    treeselect: {
      no_children_text: '没有子节点',
      no_options_text: '没有可用选项',
      no_results_text: '没有匹配的结果'
    }
  },
  documentation: {
    documentation: '文档',
    github: 'Github 地址'
  },
  permission: {
    addRole: '新增角色',
    editPermission: '编辑权限',
    roles: '你的权限',
    switchRoles: '切换权限',
    tips: '在某些情况下，不适合使用 v-permission。例如：Element-UI 的 el-tab 或 el-table-column 以及其它动态渲染 dom 的场景。你只能通过手动设置 v-if 来实现。',
    delete: '删除',
    confirm: '确定',
    cancel: '取消'
  },
  guide: {
    description: '引导页对于一些第一次进入项目的人很有用，你可以简单介绍下项目的功能。本 Demo 是基于',
    button: '打开引导'
  },
  components: {
    documentation: '文档',
    tinymceTips: '富文本是管理后台一个核心的功能，但同时又是一个有很多坑的地方。在选择富文本的过程中我也走了不少的弯路，市面上常见的富文本都基本用过了，最终权衡了一下选择了Tinymce。更详细的富文本比较和介绍见',
    dropzoneTips: '由于我司业务有特殊需求，而且要传七牛 所以没用第三方，选择了自己封装。代码非常的简单，具体代码你可以在这里看到 @/components/Dropzone',
    stickyTips: '当页面滚动到预设的位置会吸附在顶部',
    backToTopTips1: '页面滚动到指定位置会在右下角出现返回顶部按钮',
    backToTopTips2: '可自定义按钮的样式、show/hide、出现的高度、返回的位置 如需文字提示，可在外部使用Element的el-tooltip元素',
    imageUploadTips: '由于我在使用时它只有vue@1版本，而且和mockjs不兼容，所以自己改造了一下，如果大家要使用的话，优先还是使用官方版本。',
    run_once: '运行一次',
    continue: '继续',
    hour: '小时',
    minute: '分钟',
    day: '天',
    by_task_name: '通过任务名称搜索',
    delete_this_task: '确定删除该任务吗？',
    all_read_successfully: '全部已读成功',
    message_receiving_management: '消息接收管理',
    upload_failed: '上传失败',
    is_not_supported: '文件格式不支持',
    is_too_large: '文件过大',
    enter_kettle_address: '请输入 Kettle 地址',
    enter_the_port: '请输入端口',
    one_user_name: '请输入用户名',
    input_a_password: '请输入密码',
    address_is_required: 'Kettle 地址为必填',
    port_is_required: '端口为必填',
    name_is_required: '用户名为必填',
    password_is_required: '密码为必填',
    help_document_link: '帮助文档链接无效',
    such_as_dataeasedataease: '请输入登录页面标题，比如：DataEase',
    for_example_dataease: '请输入系统名称，比如：DataEase',
    time_is_required: '开始时间为必填',
    it_takes_effect: '卸载并重启服务器之后才能生效',
    uninstall_the_plugin: '确定卸载该插件？',
    uninstall: '卸载',
    relevant_content_found: '没有找到相关内容',
    view_tasks: '查看任务',
    no_classification: '暂无分类',
    no_template: '暂无模版',
    data_set_required: '数据集必填',
    unread_message: '未读消息',
    read_message: '已读消息',
    all_messages: '全部消息',
    message_list: '消息列表',
    by_plugin_name: '通过插件名称搜索',
    unable_to_uninstall: '内置插件，无法卸载',
    unable_to_update: '内置插件，无法更新',
    free: '免费',
    cost: '费用',
    developer: '开发者',
    edition: '版本',
    installation_time: '安装时间'
  },
  table: {
    dynamicTips1: '固定表头, 按照表头顺序排序',
    dynamicTips2: '不固定表头, 按照点击顺序排序',
    dragTips1: '默认顺序',
    dragTips2: '拖拽后顺序',
    title: '标题',
    importance: '重要性',
    type: '类型',
    remark: '点评',
    search: '搜索',
    add: '添加',
    export: '导出',
    reviewer: '审核人',
    id: '序号',
    date: '时间',
    author: '作者',
    readings: '阅读数',
    status: '状态',
    actions: '操作',
    edit: '编辑',
    publish: '发布',
    draft: '草稿',
    delete: '删除',
    cancel: '取 消',
    confirm: '确 定'
  },
  deDataset: {
    search_by_name: '通过名称搜索',
    new_folder: '新建文件夹',
    search_fields: '搜索字段',
    show_rows: '显示行数',
    display: '显示',
    row: '行',
    restricted_objects: '受限对象',
    select_data_source: '选择数据源',
    by_table_name: '通过表名称搜索',
    run_a_query: '运行查询',
    running_results: '运行结果',
    parameter_type: '参数类型',
    run_failed: '运行失败',
    select_data_table: '选择数据表',
    in_the_file: '文件中不能存在合并单元格',
    or_date_type: '文件的第一行为标题行，不能为空，不能为日期型',
    is_within_500m: 'Excel文件大小请确保在500M以内',
    upload_data: '上传数据',
    excel_data_first: '请先上传Excel数据',
    is_currently_available: '当前无可用的数据表',
    sure_to_synchronize: '同步字段可能导致已编辑字段发生变更，确定同步？',
    folder_name: '文件夹名称',
    folder: '所属文件夹',
    edit_folder: '编辑文件夹',
    name_already_exists: '文件夹名称已存在',
    data_preview: '数据预览',
    original_name: '原始名称',
    database: '数据库',
    selected: '已选：',
    table: '张表',
    no_dataset_click: '暂无数据集，点击',
    create: '新建',
    new_folder_first: '请先新建文件夹',
    on_the_left: '请在左侧选择数据集',
    expression_syntax_error: '字段表达式语法错误',
    create_dashboard: '创建仪表板',
    cannot_be_empty: 'SQL不能为空',
    data_reference: '数据参考',
    want_to_replace: '替换可能会影响自定义数据集、关联数据集、仪表板等，是否替换？',
    replace_the_data: '确定替换数据吗？',
    append_successfully: '追加成功',
    already_exists: '数据集名称已存在',
    edit_dataset: '编辑数据集',
    convert_to_indicator: '转换为指标',
    convert_to_dimension: '转换为维度',
    left_to_edit: '选中左侧的数据表可进行编辑',
    cannot_be_duplicate: '数据集名称不允许重复',
    set_saved_successfully: '数据集保存成功',
    to_start_using: '浏览您的数据库，表和列的内容。 选择一个数据库即可开始使用。',
    to_run_query: '点击运行查询',
    the_running_results: '即可查看运行结果',
    item: '项',
    logic_filter: '条件筛选',
    enum_filter: '枚举筛选'
  },
  detabs: {
    custom_sort: '自定义排序',
    eidttitle: '编辑标题',
    selectview: '选择视图',
    selectOthers: '选择组件',
    availableComponents: '可选组件',
    please: '未',
    head_position: '头部位置'
  },
  example: {
    warning: '创建和编辑页面是不能被 keep-alive 缓存的，因为keep-alive 的 include 目前不支持根据路由来缓存，所以目前都是基于 component name 来进行缓存的。如果你想类似的实现缓存效果，可以使用 localStorage 等浏览器缓存方案。或者不要使用 keep-alive 的 include，直接缓存所有页面。详情见'
  },
  errorLog: {
    tips: '请点击右上角bug小图标',
    description: '现在的管理后台基本都是spa的形式了，它增强了用户体验，但同时也会增加页面出问题的可能性，可能一个小小的疏忽就导致整个页面的死锁。好在 Vue 官网提供了一个方法来捕获处理异常，你可以在其中进行错误处理或者异常上报。',
    documentation: '文档介绍'
  },
  excel: {
    export: '导出',
    selectedExport: '导出已选择项',
    placeholder: '请输入文件名(默认excel-list)'
  },
  zip: {
    export: '导出',
    placeholder: '请输入文件名(默认file)'
  },
  pdf: {
    tips: '这里使用   window.print() 来实现下载pdf的功能'
  },
  theme: {
    change: '换肤',
    documentation: '换肤文档',
    tips: 'Tips: 它区别于 navbar 上的 theme-pick, 是两种不同的换肤方法，各自有不同的应用场景，具体请参考文档。',
    base: '基础配色',
    font: '字体颜色',
    border: '边框颜色',
    background: '背景颜色',
    custom: '自定义颜色',
    otherSave: '主题另存为',
    info: '主题信息',
    add: '新增主题',
    please_input_name: '请输入名称',
    name_repeat: '名称已存在'
  },
  tagsView: {
    refresh: '刷新',
    close: '关闭',
    closeOthers: '关闭其它',
    closeAll: '关闭所有'
  },
  settings: {
    title: '系统布局配置',
    theme: '主题',
    tagsView: '开启 Tags-View',
    fixedHeader: '固定 Header',
    sidebarLogo: '侧边栏 Logo'
  },
  sysParams: {
    display: '显示设置',
    ldap: 'LDAP设置',
    oidc: 'OIDC设置',
    theme: '主题设置',
    cas: 'CAS设置',
    map: '地图设置'
  },
  license: {
    i18n_no_license_record: '没有 License 记录',
    i18n_license_is_empty: 'License 为空',
    title: '授权管理',
    corporation: '客户名称',
    time: '授权时间',
    product: '产品名称',
    edition: '产品版本',
    licenseVersion: '授权版本',
    count: '授权数量',
    valid_license: '授权验证',
    show_license: '查看授权',
    valid_license_error: '授权验证失败',
    status: '授权状态',
    valid: '有效',
    invalid: '无效',
    expired: '已过期'
  },
  member: {
    create: '添加成员',
    modify: '修改成员',
    delete_confirm: '这个用户确定要删除吗?',
    please_choose_member: '请选择成员',
    search_by_name: '根据名称搜索',
    modify_personal_info: '修改个人信息',
    edit_password: '重置密码',
    edit_information: '编辑信息',
    input_name: '请输入名称',
    input_email: '请输入邮箱',
    special_characters_are_not_supported: '不支持特殊字符',
    mobile_number_format_is_incorrect: '手机号码格式不正确',
    email_format_is_incorrect: '邮箱格式不正确',
    password_format_is_incorrect: '有效密码：8-30位，英文大小写字母+数字+特殊字符（可选）',
    old_password: '旧密码',
    new_password: '新密码',
    repeat_password: '确认密码',
    inconsistent_passwords: '两次输入的密码不一致',
    remove_member: '确定要移除该成员吗',
    org_remove_member: '将该用户从组织中移除，将同时移除该组织下所有工作空间的权限，确定要移除该成员吗？',
    input_id_or_email: '请输入用户 ID, 或者 用户邮箱',
    no_such_user: '无此用户信息, 请输入正确的用户 ID 或者 用户邮箱！'
  },
  user: {
    create: '添加用户',
    modify: '编辑用户',
    input_name: '请输入姓名',
    input_id: '请输入ID',
    id_mandatory: 'ID为必填',
    name_mandatory: '姓名为必填',
    email_mandatory: '邮箱为必填',
    role_mandatory: '角色为必填',
    phone_format: '手机号码格式错误',
    input_email: '请输入邮箱',
    input_password: '请输入密码',
    input_phone: '请输入电话号码',
    input_roles: '请选择角色',
    select_users: '请选择用户',
    select_gender: '请选择性别',
    user_name_pattern_error: 'ID只能包含字母数字以及._-并以字母开头！',
    special_characters_are_not_supported: '不支持特殊字符',
    mobile_number_format_is_incorrect: '手机号码格式不正确',
    email_format_is_incorrect: '邮箱格式不正确',
    delete_confirm: '这个用户确定要删除吗?',
    apikey_delete_confirm: '这个 API Key 确定要删除吗?',
    input_id_placeholder: '请输入ID (不支持中文)',
    source: '用户来源',
    choose_org: '请选择组织',
    reset_password: '重置密码',
    current_user: '当前用户',
    origin_passwd: '原始密码',
    new_passwd: '新密码',
    confirm_passwd: '确认密码',
    change_password: '修改密码',
    search_by_name: '按姓名搜索',
    import_ldap: '导入LDAP用户',
    result_one: '个结果',
    clear_filter: '清空条件',
    recover_pwd: '是否恢复为初始密码?',
    filter_method: '筛选条件',
    filter: '筛选',
    list: '列表项',
    list_info: '请选择列表中要展示的信息',
    sure_delete: '确定删除该用户吗？',
    wecom_id: '企业微信账号',
    dingtalk_id: '钉钉账号',
    lark_id: '飞书账号',
    input_wecom_id: '请输入企业微信账号',
    input_dingtalk_id: '请输入钉钉账号',
    input_lark_id: '请输入飞书账号'
  },
  ldap: {
    url: 'LDAP地址',
    dn: '绑定DN',
    password: '密码',
    ou: '用户OU',
    filter: '用户过滤器',
    mapping: 'LDAP属性映射',
    open: '启用LDAP认证',
    input_url: '请输入LDAP地址',
    input_dn: '请输入DN',
    input_password: '请输入密码',
    input_ou: '请输入用户OU',
    input_filter: '请输入用户过滤器',
    input_mapping: '请输入LDAP属性映射',
    input_username: '请输入用户名',
    input_url_placeholder: '请输入LDAP地址 (如 ldap://localhost:389)',
    input_ou_placeholder: '输入用户OU (使用|分隔各OU)',
    input_filter_placeholder: '输入过滤器 [可能的选项是cn或uid或sAMAccountName={0}, 如：(uid={0})]',
    input_mapping_placeholder: '如：{"userName":"uid","nickName":"cn","email":"mail"}, username映射的选项可能是cn或uid或sAMAccountName',
    test_connect: '测试连接',
    test_login: '测试登录',
    edit: '编辑',
    login_success: '登录成功',
    url_cannot_be_empty: 'LDAP 地址不能为空',
    dn_cannot_be_empty: 'LDAP DN不能为空',
    ou_cannot_be_empty: 'LDAP OU不能为空',
    filter_cannot_be_empty: 'LDAP 用户过滤器不能为空',
    mapping_cannot_be_empty: 'LDAP 用户属性映射不能为空',
    password_cannot_be_empty: 'LDAP 密码不能为空'
  },
  oidc: {
    auth_endpoint: '请输入AuthEndpoint',
    token_endpoint: '请输入TokenEndpoint',
    userinfo_endpoint: '请输入UserinfoEndpoint',
    logout_endpoint: '请输入logoutEndpoint',
    clientId: '请输入ClientId',
    secret: '请输入Secret',
    scope: '请输入scope',
    redirectUrl: '请输入redirectUrl',
    input_mapping: '請輸入OIDC屬性映射',
    open: '启用OIDC认证'
  },
  role: {
    menu_authorization: '菜单授权',
    data_authorization: '数据授权',
    please_choose_role: '请选择角色',
    admin: '系统管理员',
    org_admin: '组织管理员',
    org_member: '组织成员',
    add: '新建角色',
    delete: '删除角色',
    modify: '修改角色',
    tips: '提示',
    confirm_delete: '确认删除角色 ',
    search_by_name: '按名称搜索',
    role_name: '角色名称',
    pls_input_name: '请输入名称',
    search_by_name_email: '通过姓名或邮箱搜索',
    api_role: 'API角色',
    role_exist: '添加失败，该角色已存在',
    add_api_role: '添加API角色',
    can_not_move: '不可移除，至少保留一位管理员',
    manage_can_not_move: '管理员是系统预置角色，默认拥有系统管理全部权限，无法删除',
    manage_can_not_update: '管理员是系统预置角色，默认拥有系统管理全部权限，无法编辑',
    role_description: '角色描述',
    editer_role: '编辑角色',
    add_role: '添加角色',
    role_name_exist: '该角色名称已存在',
    search_by_role: '通过角色名称搜索'
  },
  menu: {
    parent_category: '上级目录',
    module_name: '组件名称',
    module_path: '组件路径',
    route_addr: '路由地址',
    menu_sort: '菜单排序',
    authority_identification: '权限标识',
    button_name: '按钮名称',
    select_icon: '选择图标',
    create_time: '创建日期',
    tile: '菜单标题',
    create: '创建菜单',
    modify: '修改菜单',
    delete: '删除菜单',
    delete_confirm: '确定删除菜单吗',
    menu_type: '菜单类型'
  },
  organization: {
    parent_org: '上级组织',
    select_parent_org: '选择上级组织',
    top_org: '顶级组织',
    name: '组织名称',
    sort: '组织排序',
    sub_organizations: '下属组织数',
    create_time: '创建日期',
    create: '新建组织',
    modify: '修改组织',
    delete_confirm: '确定要删除该组织吗?',
    input_name: '请输入组织名称',
    select_organization: '请选择组织',
    search_by_name: '根据名称搜索',
    special_characters_are_not_supported: '格式错误(不支持特殊字符，且不能以\'-\'开头结尾)',
    select: '选择组织',
    member: '成员',
    organization: '组织',
    add_user: '添加用户',
    sure_move_user: '确定将该用户从组织中移除吗？',
    move_success: '移除成功',
    user: '用户',
    add_organization: '添加组织',
    default_organization_cannot_move: '默认组织无法删除',
    organization_name: '组织名称',
    input_organization_name: '请输入组织名称',
    relate_top_organization: '关联上级组织',
    organization_name_exist: '组织名称已存在',
    cannot_delete: '无法删除',
    remove_user_first: '请先移除组织中所有用户以及子组织，再进行删除组织操作。',
    sure_delete_organization: '确定删除该组织吗？',
    delete: '删除',
    add_child_org: '添加子组织',
    edite_organization: '编辑组织'
  },
  system_parameter_setting: {
    email_server_config: '邮箱服务器配置',
    edit_success: '编辑成功',
    mailbox_service_settings: '邮件设置',
    test_connection: '测试连接',
    SMTP_host: 'SMTP主机',
    basic_setting: '基础设置',
    front_time_out: '请求超时时间(单位:秒, 注意:保存后刷新浏览器生效)',
    msg_time_out: '消息保留时间(单位:天)',
    login_type: '默认登录方式',
    empty_front: '为空则默认取100秒',
    empty_msg: '为空则默认取30天',
    front_error: '请填写0-300正整数', // 修改了提示信息
    msg_error: '请填写1-365正整数',
    limit_times_error: '请填写1-100正整数',
    relieve_times_error: '请填写1-100正整数',
    SMTP_port: 'SMTP端口',
    SMTP_account: 'SMTP账户',
    SMTP_password: 'SMTP密码',
    SSL: '开启SSL(如果SMTP端口是465，通常需要启用SSL)',
    TLS: '开启TLS(如果SMTP端口是587，通常需要启用TLS)',
    SMTP: '是否免密 SMTP',
    host: '主机号不能为空',
    port: '端口号不能为空',
    account: '账户不能为空',
    test_recipients: '测试收件人',
    tip: '提示：仅用来作为测试邮件收件人',
    engine_mode_setting: '引擎设置',
    kettle_setting: 'Kettle 设置',
    cas_selected_warn: '选择CAS方式保存后会注销当前回话，重新登录',
    cas_reset: 'CAS切换回默认登录方式访问API：',
    main_color: '主色',
    success_color: '成功颜色',
    warning_color: '警示颜色',
    wrong_color: '错误颜色',
    main_text_color: '主要文字颜色',
    secondary_font_color: '二级字体颜色',
    prompt_font_color: '提示字体颜色',
    disable_font_color: '禁用字体颜色',
    component_stroke_color: '组件描边颜色',
    card_stroke_color: '卡片描边颜色',
    basic_white: '基础白色',
    page_background_color: '页面背景色',
    disable_background_color: '输入框禁用背景色',
    basic_black: '基础黑色',
    label_color: '标签颜色',
    system_theme: '系统主题',
    custom_theme: '自定义主题',
    color_settings: '配色设置',
    add_theme: '添加主题',
    subject_based: '基于主题',
    the_subject_name: '请输入主题名称',
    name_already_exists: '主题名称已存在',
    successfully_and_apply: '保存成功并应用',
    sure_to_exit: '你填写的信息未保存，确认退出吗？',
    copy_theme: '复制主题',
    advanced_configuration: '高级配置',
    no_custom_theme: '暂无自定义主题',
    recommended_size: '建议尺寸 ',
    support: '支持',
    no_more_than: '大小不超过',
    request_timeout: '请求超时时间',
    message_retention_time: '消息保留时间',
    log_retention_time: '日志保留时间',
    ds_check_time: '数据源检测时间间隔',
    test_mail_recipient: '仅用来作为测试邮件收件人',
    to_enable_tsl: '如果SMTP端口是587，通常需要启用TSL',
    to_enable_ssl: '如果SMTP端口是465，通常需要启用SSL',
    added_successfully: '添加成功',
    text_link_etc: '适用场景：行动号召、选中态、信息高亮、常规提示信息、文字链接等',
    prompt_and_icon: '适用场景：成功状态提示及图标',
    prompt_and_icon_danger: '适用场景：警示状态提示及图标',
    icon_danger_button: '适用场景：错误状态提示及图标、危险按钮',
    first_level_icon: '适用场景：一级标题、一级正文、重要信息展示、一级图标',
    copy_secondary_icon: '适用场景：二级标题、二级文案、二级图标',
    radio_checkbox_unchecked: '适用场景：输入框引导语、辅助文案、提示文案、三级图标、radio、checkbox未选中态',
    button_background_color: '适用场景：禁用文案、禁用图标、禁用按钮背景色',
    scenario_component_stroking: '适用场景：组件描边',
    background_header_background: '适用场景：页面背景、表头背景',
    main_background: '主背景',
    content_background: '内容背景',
    select_font: '选中字体',
    no_font_selected: '未选中字体',
    head_background: '头部背景',
    head_font: '头部字体',
    menu_floating_background: '菜单悬浮背景',
    menu_selection_background: '菜单选中背景',
    left_menu_background: '左侧菜单背景',
    left_menu_font: '左侧菜单字体',
    table_background: '表格背景',
    table_font: '表格字体',
    table_borders: '表格边框',
    subject_name: '主题名称',
    template_name: '模版名称',
    search_keywords: '搜索关键词',
    delete_this_topic: '确定删除该主题吗？',
    network_error: '网络错误',
    to_overwrite_them: '当前分类存在相同名称模版，是否覆盖？',
    import_succeeded: '导入成功',
    name_already_exists_type: '分类名称已存在',
    rename_succeeded: '重命名成功',
    the_same_category: '同一分类下，该模板名称已存在',
    delete_this_template: '确定删除该模板吗？',
    also_be_deleted: '删除后，该分类中所有的模板也将被删除。',
    delete_this_category: '确定删除该分类吗？',
    edit_template: '编辑模板',
    edit_classification: '编辑分类',
    classification_name: '分类名称',
    by_event_details: '通过事件详情搜索',
    password_input_error: '原始密码输入错误'
  },
  chart: {
    chart_refresh_tips: '视图刷新设置优先于仪表板刷新设置',
    '1-trend': '趋势',
    '2-state': '状态',
    '3-rank': '排名',
    '4-location': '位置',
    '5-weather': '天气',
    chinese: '中文',
    mark_field: '字段',
    mark_value: '值',
    function_style: '功能型样式',
    condition_style: '标记样式',
    longitude: '经度',
    latitude: '纬度',
    gradient: '渐变',
    layer_controller: '指标切换',
    suspension: '悬浮',
    chart_background: '组件背景',
    date_format: '请选择日期解析格式',
    solid_color: '纯色',
    split_gradient: '分离渐变',
    continuous_gradient: '连续渐变',
    map_center_lost: '图形缺失中心点centroid或center属性，请补全后再试',
    margin_model: '模式',
    margin_model_auto: '自动',
    margin_model_absolute: '绝对',
    margin_model_relative: '相对',
    margin_placeholder: '请输入0-100数字',
    margin_absolute_placeholder: '请输入0-40数字',
    rich_text_view_result_tips: '富文本只选取第一条结果',
    rich_text_view: '富文本视图',
    view_reset: '视图重置',
    view_reset_tips: '放弃对视图的修改？',
    export_img: '导出图片',
    title_repeat: '当前标题已存在',
    save_snapshot: '保存缩略图',
    datalist: '视图',
    add_group: '添加分组',
    add_scene: '添加场景',
    group: '分组',
    scene: '场景',
    delete: '删除',
    move_to: '移动到',
    rename: '重命名',
    tips: '提示',
    confirm_delete: '确认删除',
    delete_success: '删除成功',
    confirm: '确认',
    cancel: '取消',
    search: '搜索',
    back: '返回',
    add_table: '添加数据集',
    process: '进度',
    add_chart: '添加视图',
    db_data: '数据库数据集',
    sql_data: 'SQL数据集',
    excel_data: 'Excel数据集',
    custom_data: '自定义数据集',
    pls_slc_tbl_left: '请从左侧选视图',
    add_db_table: '添加数据库数据集',
    add_api_table: '添加API数据集',
    pls_slc_data_source: '请选择数据源',
    table: '表',
    edit: '编辑',
    create_view: '创建试图',
    data_preview: '数据预览',
    dimension: '维度',
    quota: '指标',
    title: '标题',
    show: '显示',
    chart_type: '图表类型',
    shape_attr: '图形属性',
    module_style: '组件样式',
    result_filter: '过滤器',
    x_axis: '横轴',
    y_axis: '纵轴',
    chart: '视图',
    close: '关闭',
    summary: '汇总方式',
    fast_calc: '快速计算',
    sum: '求和',
    count: '计数',
    avg: '平均',
    max: '最大值',
    min: '最小值',
    stddev_pop: '标准差',
    var_pop: '方差',
    quick_calc: '快速计算',
    show_name_set: '显示名设置',
    show_name: '显示名',
    color: '颜色',
    color_case: '配色方案',
    pls_slc_color_case: '请选择配色方案',
    color_default: '默认',
    color_retro: '复古',
    color_future: '未来',
    color_gradual: '渐变',
    color_business: '商务',
    color_gentle: '柔和',
    color_elegant: '淡雅',
    color_technology: '科技',
    color_simple: '简洁',
    not_alpha: '不透明度',
    area_border_color: '地图边线',
    area_base_color: '底图',
    size: '大小',
    bar_width: '柱宽',
    bar_gap: '柱间隔',
    adapt: '自适应',
    line_width: '线宽',
    line_type: '线型',
    line_symbol: '折点',
    line_symbol_size: '折点大小',
    line_type_solid: '实线',
    line_type_dashed: '虚线',
    line_symbol_circle: '圆形',
    line_symbol_emptyCircle: '空心圆',
    line_symbol_rect: '矩形',
    line_symbol_roundRect: '圆角矩形',
    line_symbol_triangle: '三角形',
    line_symbol_diamond: '菱形',
    line_symbol_pin: '钉子',
    line_symbol_arrow: '箭头',
    line_symbol_none: '无',
    line_area: '面积',
    pie_inner_radius: '内径',
    pie_outer_radius: '外径',
    funnel_width: '宽度',
    line_smooth: '平滑折线',
    title_style: '标题样式',
    text_fontsize: '字体大小',
    text_color: '字体颜色',
    text_h_position: '水平位置',
    text_v_position: '垂直位置',
    text_pos_left: '左',
    text_pos_center: '中',
    text_pos_right: '右',
    text_pos_top: '上',
    text_pos_bottom: '下',
    text_italic: '字体倾斜',
    italic: '倾斜',
    orient: '方向',
    horizontal: '水平',
    vertical: '垂直',
    legend: '图例',
    shape: '形状',
    polygon: '多边形',
    circle: '圆形',
    label: '标签',
    label_position: '标签位置',
    label_bg: '标签背景',
    label_shadow: '标签阴影',
    label_shadow_color: '阴影颜色',
    label_reserve_decimal_count: '保留小数',
    content_formatter: '内容格式',
    inside: '内',
    tooltip: '提示',
    tooltip_item: '数据项',
    tooltip_axis: '坐标轴',
    formatter_plc: '内容格式为空时，显示默认格式',
    xAxis: '横轴',
    yAxis: '纵轴',
    position: '位置',
    rotate: '角度',
    name: '名称',
    icon: '图标',
    trigger_position: '触发位置',
    asc: '升序',
    desc: '降序',
    sort: '排序',
    filter: '过滤',
    none: '无',
    background: '背景',
    border: '边角',
    border_width: '边框宽度',
    border_radius: '边框半径',
    alpha: '透明度',
    add_filter: '添加过滤',
    no_limit: '无限制',
    filter_eq: '等于',
    filter_not_eq: '不等于',
    filter_lt: '小于',
    filter_le: '小于等于',
    filter_gt: '大于',
    filter_ge: '大于等于',
    filter_null: '为空',
    filter_not_null: '不为空',
    filter_empty: '空字符串',
    filter_not_empty: '非空字符串',
    filter_include: '包含',
    filter_not_include: '不包含',
    rose_type: '玫瑰图模式',
    radius_mode: '半径',
    area_mode: '面积',
    rose_radius: '圆角',
    view_name: '视图标题',
    belong_group: '所属分组',
    select_group: '选择分组',
    name_can_not_empty: '名称不能为空',
    template_can_not_empty: '请选择仪表版',
    custom_count: '记录数',
    table_title_fontsize: '表头字体大小',
    table_item_fontsize: '表格字体大小',
    table_header_bg: '表头背景',
    table_item_bg: '表格背景',
    table_header_font_color: '表头字体',
    table_item_font_color: '表格字体',
    table_show_index: '显示序号',
    stripe: '斑马纹',
    start_angle: '起始角度',
    end_angle: '结束角度',
    style_priority: '样式优先级',
    dashboard: '仪表板',
    dimension_color: '名称颜色',
    quota_color: '值颜色',
    dimension_font_size: '名称字体大小',
    quota_font_size: '值字体大小',
    space_split: '名称/值间隔',
    only_one_quota: '仅支持1个指标',
    only_one_result: '仅显示第1个计算结果',
    dimension_show: '名称显示',
    quota_show: '值显示',
    title_limit: '标题不能大于50个字符',
    filter_condition: '过滤条件',
    filter_field_can_null: '过滤字段必填',
    preview_100_data: '预览前100条记录',
    chart_table_normal: '汇总表',
    chart_table_info: '明细表',
    chart_card: '指标卡',
    chart_bar: '基础柱状图',
    chart_bar_stack: '堆叠柱状图',
    chart_percentage_bar_stack: '百分比柱状图',
    chart_bar_horizontal: '横向柱状图',
    chart_bar_stack_horizontal: '横向堆叠柱状图',
    chart_percentage_bar_stack_horizontal: '横向百分比柱状图',
    chart_line: '基础折线图',
    chart_line_stack: '堆叠折线图',
    chart_pie: '饼图',
    chart_pie_donut: '环形图',
    chart_pie_rose: '南丁格尔玫瑰图',
    chart_pie_donut_rose: '南丁格尔玫瑰环形图',
    chart_funnel: '漏斗图',
    chart_radar: '雷达图',
    chart_gauge: '仪表盘',
    chart_map: '地图',
    dateStyle: '日期显示',
    datePattern: '日期格式',
    y: '年',
    y_M: '年月',
    y_Q: '年季度',
    y_W: '年周',
    y_M_d: '年月日',
    H_m_s: '时分秒',
    y_M_d_H_m: '年月日时分',
    y_M_d_H_m_s: '年月日时分秒',
    date_sub: 'yyyy-MM-dd',
    date_split: 'yyyy/MM/dd',
    chartName: '新建视图',
    chart_show_error: '无法正常显示',
    chart_error_tips: '如有疑问请联系管理员',
    title_cannot_empty: '标题不能为空',
    table_title_height: '表头行高',
    table_item_height: '表格行高',
    axis_show: '轴线显示',
    axis_color: '轴线颜色',
    axis_width: '轴线宽度',
    axis_type: '轴线类型',
    grid_show: '网格线显示',
    grid_color: '网格线颜色',
    grid_width: '网格线宽度',
    grid_type: '网格线类型',
    axis_type_solid: '实线',
    axis_type_dashed: '虚线',
    axis_type_dotted: '点',
    axis_label_show: '标签显示',
    axis_label_color: '标签颜色',
    axis_label_fontsize: '标签大小',
    text_style: '字体样式',
    bolder: '加粗',
    change_ds: '更换数据集',
    change_ds_tip: '提示：更换数据集将导致字段发生变化，需重新制作视图',
    axis_name_color: '名称颜色',
    axis_name_fontsize: '名称字体',
    pie_label_line_show: '引导线',
    outside: '外',
    center: '中心',
    split: '轴线',
    axis_line: '轴线',
    axis_label: '轴标签',
    label_fontsize: '标签大小',
    split_line: '分割线',
    split_color: '分割颜色',
    shadow: '阴影',
    condition: '过滤值',
    filter_value_can_null: '过滤值不能为空',
    filter_like: '包含',
    filter_not_like: '不包含',
    filter_in: '属于',
    filter_not_in: '不属于',
    color_light: '明亮',
    color_classical: '经典',
    color_fresh: '清新',
    color_energy: '活力',
    color_red: '火红',
    color_fast: '轻快',
    color_spiritual: '灵动',
    chart_details: '视图明细',
    export: '导出',
    details: '明细',
    image: '图片',
    export_details: '导出明細',
    chart_data: '数据',
    chart_style: '样式',
    drag_block_type_axis: '类别轴',
    drag_block_value_axis: '值轴',
    drag_block_table_data_column: '数据列',
    drag_block_pie_angel: '扇区角度',
    drag_block_pie_label: '扇区标签',
    drag_block_gauge_angel: '指针角度',
    drag_block_label_value: '值',
    drag_block_funnel_width: '漏斗层宽',
    drag_block_funnel_split: '漏斗分层',
    drag_block_radar_length: '分支长度',
    drag_block_radar_label: '分支标签',
    map_range: '地图范围',
    select_map_range: '请选择地图范围',
    area: '地区',
    stack_item: '堆叠项',
    placeholder_field: '拖动字段至此处',
    axis_label_rotate: '标签角度',
    chart_scatter_bubble: '气泡图',
    chart_scatter: '散点图',
    bubble_size: '气泡大小',
    chart_treemap: '矩形树图',
    drill: '钻取',
    drag_block_treemap_label: '色块标签',
    drag_block_treemap_size: '色块大小',
    bubble_symbol: '图形',
    gap_width: '间隔',
    width: '宽度',
    height: '高度',
    system_case: '系统方案',
    custom_case: '自定义',
    last_layer: '当前已经是最后一级',
    radar_size: '大小',
    chart_mix: '组合图',
    axis_value: '轴值',
    axis_value_min: '最小值',
    axis_value_max: '最大值',
    axis_value_split: '间隔',
    axis_auto: '自动',
    table_info_switch: '明细表切换将清空维度',
    drag_block_value_axis_main: '主轴值',
    drag_block_value_axis_ext: '副轴值',
    yAxis_main: '主纵轴',
    yAxis_ext: '副纵轴',
    total: '共',
    items: '条数据',
    chart_liquid: '水波图',
    drag_block_progress: '进度指示',
    liquid_max: '目标值',
    liquid_outline_border: '边框粗细',
    liquid_outline_distance: '边框间隔',
    liquid_wave_length: '水波长度',
    liquid_wave_count: '水波数量',
    liquid_shape: '形状',
    liquid_shape_circle: '圆形',
    liquid_shape_diamond: '菱形',
    liquid_shape_triangle: '三角形',
    liquid_shape_pin: '气球',
    liquid_shape_rect: '矩形',
    dimension_or_quota: '维度或指标',
    axis_value_split_count: '刻度数',
    axis_value_split_space: '刻度间距',
    chart_waterfall: '瀑布图',
    pie_inner_radius_percent: '内径占比',
    pie_outer_radius_size: '外径大小',
    table_page_size: '分页',
    table_page_size_unit: '条/页',
    result_count: '结果展示',
    result_mode_all: '全部',
    result_mode_custom: '自定义',
    chart_word_cloud: '词云',
    drag_block_word_cloud_label: '词标签',
    drag_block_word_cloud_size: '词大小',
    splitCount_less_100: '刻度数范围0-100',
    change_chart_type: '更改类型',
    chart_type_table: '表格',
    chart_type_quota: '指标',
    chart_type_trend: '趋势',
    chart_type_compare: '比较',
    chart_type_distribute: '分布',
    chart_type_relation: '关系',
    chart_type_space: '空间位置',
    preview: '上一步',
    next: '下一步',
    select_dataset: '选择数据集',
    select_chart_type: '选择图表类型',
    recover: '重置',
    yoy_label: '同比/环比',
    yoy_setting: '同环比设置',
    pls_select_field: '请选择字段',
    compare_date: '对比日期',
    compare_type: '对比类型',
    compare_data: '数据设置',
    year_yoy: '年同比',
    month_yoy: '月同比',
    quarter_yoy: '季同比',
    week_yoy: '周同比',
    day_yoy: '日同比',
    year_mom: '年环比',
    month_mom: '月环比',
    quarter_mom: '季环比',
    week_mom: '周环比',
    day_mom: '日环比',
    data_sub: '对比差值',
    data_percent: '差值百分比',
    compare_calc_expression: '计算公式',
    and: '与',
    or: '或',
    logic_exp: '逻辑条件',
    enum_exp: '字段枚举值',
    pls_slc: '请选择',
    filter_exp: '过滤值',
    filter_type: '过滤方式',
    filter_value_can_not_str: '数值类型字段过滤值不能包含文本',
    enum_value_can_not_null: '字段枚举值不能为空',
    table_config: '表格配置',
    table_column_width_config: '列宽调整',
    table_column_adapt: '自适应',
    table_column_custom: '自定义',
    chart_table_pivot: '透视表',
    table_pivot_row: '数据行',
    field_error_tips: '该字段所对应的数据集原始字段发生变更（包括维度、指标，字段类型，字段被删除等），建议重新编辑',
    mark_field_error: '数据集变更，当前字段不存在，请重新选择',
    table_border_color: '边框颜色',
    table_header_align: '表头对齐方式',
    table_item_align: '表格对齐方式',
    table_align_left: '左对齐',
    table_align_center: '居中',
    table_align_right: '右对齐',
    table_scroll_bar_color: '滚动条颜色',
    draw_back: '收回',
    senior: '高级',
    senior_cfg: '高级设置',
    function_cfg: '功能设置',
    analyse_cfg: '分析预警',
    slider: '缩略轴',
    slider_range: '默认范围',
    slider_bg: '背景',
    slider_fill_bg: '选中背景',
    slider_text_color: '字体颜色',
    chart_no_senior: '当前图表类型暂无高级配置，敬请期待',
    chart_no_properties: '当前图表类型暂无样式配置',
    assist_line: '辅助线',
    field_fixed: '固定值',
    line_type_dotted: '点',
    value_can_not_empty: '值不能为空',
    value_error: '值必须为数值',
    threshold: '阈值',
    threshold_range: '阈值区间',
    gauge_threshold_format_error: '格式错误',
    total_cfg: '总计配置',
    col_cfg: '列汇总',
    row_cfg: '行汇总',
    total_show: '总计',
    total_position: '位置',
    total_label: '别名',
    sub_total_show: '小计',
    total_pos_top: '顶部',
    total_pos_bottom: '底部',
    total_pos_left: '左侧',
    total_pos_right: '右侧',
    chart_label: '文本卡',
    drag_block_label: '标签',
    count_distinct: '去重计数',
    table_page_mode: '分页模式',
    page_mode_page: '翻页',
    page_mode_pull: '下拉',
    exp_can_not_empty: '条件不能为空',
    value_formatter: '数值格式',
    value_formatter_type: '格式类型',
    value_formatter_auto: '自动',
    value_formatter_value: '数值',
    value_formatter_percent: '百分比',
    value_formatter_unit: '数量单位',
    value_formatter_decimal_count: '小数位数',
    value_formatter_suffix: '单位后缀',
    value_formatter_thousand_separator: '千分符',
    value_formatter_example: '示例',
    unit_none: '无',
    unit_thousand: '千',
    unit_ten_thousand: '万',
    unit_million: '百万',
    unit_hundred_million: '亿',
    formatter_decimal_count_error: '请输入0-10的整数',
    gauge_threshold_compare_error: '阈值范围需逐级递增',
    tick_count: '刻度间隔数',
    custom_sort: '自定义',
    custom_sort_tip: '自定义排序优先级最高，且仅支持单个字段自定义',
    clean_custom_sort: '清除自定义排序',
    ds_field_edit: '数据集字段管理',
    chart_field_edit: '视图字段管理',
    copy_field: '复制字段',
    calc_field: '计算字段',
    form_type: '类别',
    scroll_cfg: '滚动设置',
    scroll: '滚动',
    open: '开启',
    row: '行数',
    interval: '间隔',
    max_more_than_mix: '最大值必须大于最小值',
    field: '字段',
    textColor: '文字颜色',
    backgroundColor: '背景颜色',
    field_can_not_empty: '字段不能为空',
    conditions_can_not_empty: '字段的条件不能为空，若无条件，请直接删除该字段',
    remark: '备注',
    remark_edit: '编辑备注',
    remark_bg_color: '背景填充',
    quota_font_family: '值字体',
    quota_text_style: '值样式',
    quota_letter_space: '值字间距',
    dimension_font_family: '名称字体',
    dimension_text_style: '名称样式',
    dimension_letter_space: '名称字间距',
    font_family: '字体',
    letter_space: '字间距',
    font_shadow: '字体阴影',
    chart_area: '面积图',
    fix: '固定值',
    dynamic: '动态值',
    gauge_size_field_delete: '动态值中字段发生变更，请重新编辑',
    chart_group: '子类别',
    chart_bar_group: '分组柱状图',
    chart_bar_group_stack: '分组堆叠柱状图',
    field_dynamic: '动态值',
    aggregation: '聚合方式',
    filter_between: '介于',
    field_not_empty: '字段不能为空',
    summary_not_empty: '聚合方式不能为空',
    reserve_zero: '取整',
    reserve_one: '一位',
    reserve_two: '两位',
    proportion: '占比',
    label_content: '标签展示',
    percent: '占比',
    table_index_desc: '表头名称',
    total_sort: '总计排序',
    total_sort_none: '无',
    total_sort_asc: '升序',
    total_sort_desc: '降序',
    total_sort_field: '排序字段',
    empty_data_strategy: '空值处理',
    break_line: '保持为空',
    set_zero: '置为0',
    ignore_data: '隐藏空值',
    sub_dimension_tip: '该字段为必填项，且不应使用类别轴中的字段，若无需该字段，请选择基础图表进行展示，否则展示效果不理想。',
    drill_dimension_tip: '钻取字段仅支持数据集中的字段',
    table_scroll_tip: '明细表仅在分页模式为"下拉"时生效。',
    table_threshold_tip: '提示：请勿重复选择字段，若同一字段重复配置，则只有最后的字段配置生效',
    table_column_width_tip: '列宽并非任何时候都能生效。<br/>容器宽度优先级高于列宽，即(表格容器宽度 / 列数 > 指定列宽)，则列宽优先取(容器宽度 / 列数)。',
    reference_field_tip: '引用字段以 "[" 开始， "]" 结束。<br/>请勿修改引用内容，否则将引用失败。<br/>若输入与引用字段相同格式的内容，将被当作引用字段处理。',
    scatter_tip: '该指标生效时，样式大小中的气泡大小属性将失效',
    place_name_mapping: '地名映射',
    axis_tip: '最小值、最大值、间隔均为数值类型；若不填，则该项视为自动。<br/>请确保填写数值能正确计算，否则将无法正常显示轴值。',
    format_tip: `模板变量有 {a}, {b}，{c}，{d}，分别表示系列名，数据名，数据值等。<br>
                  在 触发位置 为 '坐标轴' 的时候，会有多个系列的数据，此时可以通过 {a0}, {a1}, {a2} 这种后面加索引的方式表示系列的索引。<br>
                  不同图表类型下的 {a}，{b}，{c}，{d} 含义不一样。 其中变量{a}, {b}, {c}, {d}在不同图表类型下代表数据含义为：<br><br>
                  折线（区域）图、柱状（条形）图、仪表盘 : {a}（系列名称），{b}（类目值），{c}（数值）<br>
                  饼图、漏斗图: {a}（系列名称），{b}（数据项名称），{c}（数值）, {d}（百分比）<br>
                  地图 : {a}（系列名称），{b}（区域名称），{c}（合并数值）, {d}（无）<br>
                  散点图（气泡）图 : {a}（系列名称），{b}（数据名称），{c}（数值数组）, {d}（无）`
  },
  dataset: {
    spend_time: '耗时',
    sql: 'SQL 语句',
    sql_result: '运行结果',
    parse_filed: '解析字段',
    field_rename: '字段重命名',
    params_work: '仅在编辑sql时生效',
    select_year: '选择年',
    sql_variable_limit_1: '1、SQL 变量只能在 WHERE 条件中使用',
    sql_variable_limit_2: '2、示例：select * from table_name where column_name1=\'${param_name1}\' and column_name2 in \'${param_name2}\'',
    select_month: '选择月',
    select_date: '选择日期',
    select_time: '选择时间',
    time_year: '日期-年',
    time_year_month: '日期-年月',
    time_year_month_day: '日期-年月日',
    time_all: '日期-年月日时分秒',
    dataset_sync: ' ( 数据同步中... )',
    sheet_warn: '有多个 Sheet 页，默认抽取第一个',
    datalist: '数据集',
    name: '数据集名称',
    add_group: '添加分组',
    add_scene: '添加场景',
    group: '分组',
    scene: '场景',
    delete: '删除',
    move_to: '移动到',
    rename: '重命名',
    tips: '提示',
    confirm_delete: '确认删除',
    confirm_delete_msg: '数据集删除，会影响与其相关的自定义数据集、关联数据集、仪表板，确认删除？',
    delete_success: '删除成功',
    confirm: '确认',
    cancel: '取消',
    search: '搜索',
    back: '返回',
    add_table: '添加数据集',
    process: '进度',
    update: '更新',
    db_data: '数据库数据集',
    sql_data: 'SQL 数据集',
    excel_data: 'Excel 数据集',
    custom_data: '自定义数据集',
    pls_slc_tbl_left: '请从左侧选择表',
    add_db_table: '添加数据库数据集',
    add_api_table: '添加API数据集',
    pls_slc_data_source: '请选择数据源',
    table: '表',
    edit: '编辑',
    create_view: '创建视图',
    data_preview: '数据预览',
    field_type: '字段类型',
    field_name: '字段名称',
    field_origin_name: '原始名称',
    field_check: '选中',
    update_info: '更新信息',
    update_records: '更新记录',
    join_view: '数据关联',
    text: '文本',
    time: '时间',
    value: '数值',
    mode: '模式',
    direct_connect: '直连',
    sync_data: '定时同步',
    update_setting: '更新设置',
    sync_now: '立即更新',
    add_task: '添加任务',
    task_name: '任务名称',
    task_id: '任务ID',
    start_time: '开始时间',
    end_time: '结束时间',
    status: '状态',
    error: '失败',
    completed: '成功',
    underway: '执行中',
    task_update: '更新设置',
    update_type: '更新方式',
    all_scope: '全量更新',
    add_scope: '增量更新',
    select_data_time: '选择日期时间',
    execute_rate: '执行频率',
    execute_once: '立即执行',
    simple_cron: '简单重复',
    cron_config: '表达式设定',
    no_limit: '无限制',
    set_end_time: '设定结束时间',
    operate: '操作',
    save_success: '保存成功',
    close: '关闭',
    required: '必填',
    input_content: '请输入内容',
    add_sql_table: '添加 SQL 数据集',
    preview: '预览',
    pls_input_name: '请输入名称',
    connect_mode: '连接模式',
    incremental_update_type: '增量更新方式',
    incremental_add: '增量添加',
    incremental_delete: '增量删除',
    last_update_time: '上次更新时间',
    current_update_time: '当前更新时间',
    param: '参数',
    edit_sql: '编辑 SQL 数据集',
    showRow: '显示行',
    add_excel_table: '添加Excel数据集',
    add_custom_table: '添加自定义数据集',
    upload_file: '上传文件',
    detail: '详情',
    type: '类型',
    create_by: '创建者',
    create_time: '创建时间',
    preview_show: '显示',
    preview_item: '条数据',
    preview_total: '共',
    pls_input_less_5: '请输入5位以内的正整数',
    field_edit: '编辑字段',
    table_already_add_to: '该表已添加至',
    uploading: '上传中...',
    add_union: '新建关联',
    union_setting: '关联设置',
    pls_slc_union_field: '请选择关联字段',
    pls_slc_union_table: '请选择关联表',
    source_table: '关联表',
    source_field: '关联字段',
    target_table: '被关联表',
    target_field: '被关联字段',
    union_relation: '关联关系',
    pls_setting_union_success: '请正确设置关联关系',
    invalid_dataset: 'Kettle未运行，无效数据集',
    check_all: '全选',
    can_not_union_self: '被关联表不能与关联表相同',
    float: '小数',
    edit_custom_table: '编辑自定义数据集',
    edit_field: '编辑字段',
    preview_100_data: '显示前100行数据',
    invalid_table_check: '非直连数据集请先完成数据同步',
    parse_error: 'Excel解析失败，请检查格式、字段等信息。具体参考：https://dataease.io/docs/user_manual/dataset_configuration/dataset_Excel',
    origin_field_type: '字段原始类型',
    edit_excel_table: '编辑Excel数据集',
    edit_excel: '编辑Excel',
    excel_replace: '替换',
    excel_add: '追加',
    dataset_group: '数据集分组',
    m1: '将 ',
    m2: ' 移动到',
    char_can_not_more_50: '数据集名称不能超过50个字符',
    task_add_title: '添加任务',
    task_edit_title: '编辑任务',
    sync_latter: '稍后同步',
    task: {
      list: '任务列表',
      record: '执行记录',
      create: '新建任务',
      name: '任务名称',
      last_exec_time: '上次执行时间',
      next_exec_time: '下次执行时间',
      last_exec_status: '上次执行结果',
      task_status: '任务状态',
      dataset: '数据集',
      search_by_name: '根据名称搜索',
      underway: '等待执行',
      stopped: '执行结束',
      exec: '执行中',
      pending: '暂停',
      confirm_exec: '手动触发执行？',
      change_success: '状态切换成功',
      excel_replace_msg: '可能会影响自定义数据集、关联数据集、仪表板等，确认替换？',
      effect_ext_field: '会影响计算字段'
    },
    field_group_type: '分类',
    location: '地理位置',
    left_join: '左连接',
    right_join: '右连接',
    inner_join: '内连接',
    full_join: '全连接',
    can_not_union_diff_datasource: '被关联数据集必须与当前数据集的数据源一致',
    operator: '操作',
    d_q_trans: '维度/指标转换',
    add_calc_field: '新建计算字段',
    input_name: '请输入名称',
    field_exp: '字段表达式',
    data_type: '数据类型',
    click_ref_field: '点击引用字段',
    click_ref_function: '点击引用函数',
    field_manage: '字段管理',
    edit_calc_field: '编辑计算字段',
    calc_field: '计算字段',
    show_sql: '显示SQL',
    ple_select_excel: '请选择要导入的 Excel',
    merge: '合并',
    no_merge: '不合并',
    merge_msg: '数据表中存在字段一致的情况，是否合并到一个数据集中?',
    merge_title: '合并数据',
    field_name_less_50: '字段名不能超过50个字符',
    excel_info_1: '1、Excel 文件中不能存在合并单元格；',
    excel_info_2: '2、Excel 文件的第一行为标题行，不能为空，不能为日期型；',
    excel_info_3: '3、文件大小请确保在500M以内。',
    sync_field: '同步字段',
    confirm_sync_field: '确认同步',
    confirm_sync_field_tips: '同步字段可能会导致已编辑字段发生变更，请确认',
    sync_success: '同步成功',
    sync_success_1: '同步成功，请对当前数据集重新执行数据同步操作',
    row_permission: {
      type: '类型',
      name: '名称',
      condition: '条件',
      value: '值',
      add: '添加行权限',
      edit: '编辑行权限',
      please_select_field: '请选择字段',
      please_select_auth_type: '请选择授权类型',
      please_select_auth_id: '请选择授权目标',
      row_permission_not_empty: '行权限不能为空',
      search_by_filed_name: '根据字段名称搜索',
      auth_type: '授权类型',
      auth_obj: '授权对象'
    },
    column_permission: {
      add: '添加列权限',
      edit: '编辑列权限',
      please_select_field: '请选择字段',
      please_select_auth_type: '请选择授权类型',
      please_select_auth_id: '请选择授权目标',
      column_permission_not_empty: '列权限不能为空',
      auth_type: '授权类型',
      auth_obj: '授权对象',
      enable: '启用',
      disable: '禁用',
      prohibit: '禁用',
      desensitization: '脱敏',
      desensitization_rule: '脱敏规则',
      m: 'M等于',
      n: 'N等于',
      mgtn: 'M 不能大于 N'
    },
    row_permissions: '行权限',
    column_permissions: '列权限',
    row_column_permissions: '行列权限',
    union_data: '关联数据集',
    add_union_table: '添加关联数据集',
    edit_union: '编辑关联数据集',
    union: '关联',
    edit_union_relation: '编辑关联关系',
    add_union_relation: '新建关联关系',
    field_select: '字段选择',
    add_union_field: '添加关联字段',
    union_error: '关联关系与关联字段不能为空',
    union_repeat: '当前数据集已被关联，请勿重复关联',
    preview_result: '预览结果',
    sql_ds_union_error: '直连模式下SQL数据集，不支持关联',
    api_data: 'API 数据集',
    copy: '复制',
    sync_log: '同步日志',
    field_edit_name: '字段名称',
    input_edit_name: '请输入字段名称',
    edit_search: '通过名称搜索',
    na: '暂无',
    date_format: '时间格式，默认: 年-月-日 时:分:秒',
    export_dataset: '数据集导出',
    filename: '文件名称',
    export_filter: '筛选条件',
    pls_input_filename: '请输入文件名称',
    calc_tips: {
      tip1: '表达式语法请遵循该数据源对应的数据库语法。',
      tip2: '数据集中不支持聚合运算。',
      tip3: '引用字段以 "[" 开始， "]" 结束',
      tip4: '请勿修改引用内容，否则将引用失败',
      tip5: '若输入与引用字段相同格式的内容，将被当作引用字段处理',
      tip6: '使用数据集对应数据库类型所支持的函数，语法同对应数据库',
      tip7: '如日期格式化：MySQL使用DATE_FORMAT(date,format)；Oracle使用TO_DATE(X,[,fmt])',
      tip8: '非直连模式数据集，使用Doris数据库函数，可参考Doris官网'
    }
  },
  driver: {
    driver: '驱动',
    please_choose_driver: '请选择驱动',
    mgm: '驱动管理',
    exit_mgm: '退出驱动管理',
    add: '添加驱动',
    modify: '修改',
    show_info: '驱动信息',
    file_name: '文件名',
    version: '版本',
    please_set_driverClass: '请指定驱动类'
  },
  datasource: {
    data_source_configuration: '数据源配置',
    data_source_table: '数据源表',
    auth_method: '认证方式',
    passwd: '用户名密码',
    kerbers_info: '请确保 krb5.Conf、Keytab Key，已经添加到路径：/opt/dataease/conf',
    client_principal: 'Client Principal',
    keytab_Key_path: 'Keytab Key Path',
    datasource: '数据源',
    please_select_left: '请从左侧选择',
    show_info: '数据源信息',
    create: '新建数据源',
    type: '类型',
    please_choose_type: '请选择数据源类型',
    please_choose_data_type: '请选择计算模式',
    data_base: '数据库名称',
    user_name: '用户名',
    password: '密码',
    host: '主机名/IP地址',
    doris_host: 'Doris 地址',
    query_port: 'Query Port',
    http_port: 'Http Port',
    port: '端口',
    datasource_url: '地址',
    please_input_datasource_url: '请输入 Elasticsearch 地址，如: http://es_host:es_port',
    please_input_data_base: '请输入数据库名称',
    please_select_oracle_type: '选择连接类型',
    please_input_user_name: '请输入用户名',
    please_input_password: '请输入密码',
    please_input_host: '请输入主机',
    please_input_url: '请输入URL地址',
    please_input_port: '请输入端口',
    modify: '编辑数据源',
    copy: '复制数据源',
    validate_success: '校验成功',
    validate: '校验',
    search_by_name: '根据名称搜索',
    delete_warning: '确定要删除吗?',
    input_name: '请输入名称',
    input_limit_2_25: '2-25字符',
    input_limit_2_50: '2-50字符',
    input_limit: '{num}字符',
    oracle_connection_type: '服务名/SID',
    oracle_sid: 'SID',
    oracle_service_name: '服务名',
    get_schema: '获取 Schema',
    schema: 'Schema',
    charset: '字符集',
    targetCharset: '目标字符集',
    please_choose_schema: '请选择数据库 Schema',
    please_choose_charset: '请选择数据库字符集',
    please_choose_targetCharset: '请选择目标字符集',
    edit_datasource_msg: '修改数据源信息，可能会导致该数据源下的数据集不可用，确认修改？',
    repeat_datasource_msg: '已经存在相同配置的数据源信息, ',
    confirm_save: '确认保存?',
    in_valid: '无效数据源',
    initial_pool_size: '初始连接数',
    min_pool_size: '最小连接数',
    max_pool_size: '最大连接数',
    max_idle_time: '最大空闲(秒)',
    bucket_num: 'Bucket 数量',
    replication_num: '副本数量',
    please_input_bucket_num: '请输入 Bucket 数量',
    please_input_replication_num: '请输入副本数量',
    acquire_increment: '增长数',
    connect_timeout: '连接超时(秒)',
    please_input_initial_pool_size: '请输入初始连接数',
    please_input_min_pool_size: '请输入最小连接数',
    please_input_max_pool_size: '请输入最大连接数',
    please_input_max_idle_time: '请输入最大空闲(秒)',
    please_input_acquire_increment: '请输入增长数',
    please_input_query_timeout: '请输入查询超时',
    please_input_connect_timeout: '请输入连接超时(秒)',
    no_less_then_0: '高级设置中的参数不能小于零',
    port_no_less_then_0: '端口不能小于零',
    priority: '高级设置',
    data_mode: '数据模式',
    direct: '直连模式',
    extract: '抽取模式',
    all_compute_mode: '直连、抽取模式',
    extra_params: '额外的JDBC连接字符串',
    please_input_dataPath: '请输入 JsonPath 数据路径',
    warning: '包含无效数据表',
    data_table: '数据表',
    data_table_name: '数据表名称',
    method: '请求方式',
    url: 'URL',
    add_api_table: '添加API数据表',
    edit_api_table: '编辑API数据表',
    base_info: '基础信息',
    column_info: '数据结构',
    request: '请求',
    path_all_info: '请填入完整地址',
    req_param: '请求参数',
    headers: '请求头',
    key: '键',
    value: '值',
    data_path: '提取数据',
    data_path_desc: '请用JsonPath填写数据路径',
    body_form_data: 'form-data',
    body_x_www_from_urlencoded: 'x-www-form-urlencoded',
    body_json: 'json',
    body_xml: 'xml',
    body_raw: 'row',
    request_body: '请求体',
    auth_config: '认证配置',
    auth_config_info: '请求需要进行权限校验',
    verified: '认证',
    verification_method: '认证方式',
    username: '用户名',
    api_table_not_empty: 'API 数据表不能为空',
    has_repeat_name: 'API 数据表名称重复',
    has_repeat_field_name: '字段名重复，请修改后再选择',
    api_field_not_empty: '字段不能为空',
    success_copy: '复制成功',
    valid: '有效',
    invalid: '无效',
    api_step_1: '连接API',
    api_step_2: '提取数据',
    _ip_address: '请输入主机名/IP地址',
    display_name: '显示名称',
    connection_mode: '连接方式',
    driver_file: '驱动文件',
    edit_driver: '编辑驱动',
    driver_name: '驱动名称',
    drive_type: '驱动类型',
    add_driver: '添加驱动',
    diver_on_the_left: '请在左侧选择驱动',
    no_data_table: '暂无数据表',
    on_the_left: '请在左侧选择数据源',
    table_name: '表名称',
    create_dataset: '创建数据集',
    field_description: '字段描述',
    table_description: '表描述',
    relational_database: '关系型数据库',
    data_warehouse_lake: '数仓/数据湖',
    non_relational_database: '非关系型数据库',
    all: '所有',
    other: '其他',
    this_data_source: '确定删除该数据源吗？',
    delete_this_dataset: '确定删除该数据集吗？',
    cannot_be_deleted_dataset: '该数据集存在如下血缘关系，删除会造成相关仪表板的视图失效，确定删除？',
    cannot_be_deleted_datasource: '该数据源存在如下血缘关系，删除会造成相关仪表板的视图失效，确定删除？',
    edit_folder: '编辑文件夹',
    click_to_check: '点击去查看血缘关系',
    please_select: '请选择',
    delete_this_item: '是否要删除此项？',
    can_be_uploaded: '仅支持上传JAR格式的文件',
    query_timeout: '查询超时',
    add_data_source: '添加数据源',
    delete_this_driver: '确定删除该驱动吗？',
    basic_info: '基本信息'
  },
  pblink: {
    key_pwd: '请输入密码打开链接',
    input_placeholder: '请输入4位数字或字母',
    pwd_error: '密码错误',
    pwd_format_error: '请输入4位数字或字母',
    sure_bt: '确定',
    back_parent: '返回上一级'
  },
  panel: {
    url_check_error: '跳转错误，URL不合法',
    view_style: '视图样式',
    view_color_setting: '视图配色',
    border_color_setting: '边框配色',
    unpublished_tips: '取消发布后，该仪表板不能被查看。确定要取消发布？',
    position_adjust_component: '位置调整',
    active_font_size: '选中字体大小',
    carousel: '轮播',
    switch_time: '切换时间',
    position_adjust: '位置',
    space_top: '上',
    space_left: '左',
    space_width: '宽',
    space_height: '高',
    to_top: '置顶',
    down: '下载',
    mobile_style_setting: '样式设置',
    mobile_style_setting_tips: '自定义移动端背景',
    board: '边框',
    text: '文字',
    board_background: '背景',
    title_color: '标题颜色',
    input_style: '输入框样式(颜色)',
    overall_setting: '整体配置',
    panel_background: '仪表板背景',
    component_color: '组件配色',
    chart_title: '图表标题',
    filter_component: '过滤组件',
    enable_refresh_view: '开启刷新',
    enable_view_loading: '视图加载提示',
    image_size_tips: '图片请不要大于15M',
    image_add_tips: '只能插入图片',
    watermark: '水印',
    panel_get_data_error: '获取仪表板信息失败，仪表板可能已经被删除，请检查仪表板状态',
    panel_no_save_tips: '存在未保存的仪表板',
    panel_cache_use_tips: '检查到上次有仪表板未能正常保存，是否使用上次未保存的仪表板？',
    template_name_tips: '仪表板名称必填',
    panel_background_item: '自定义仪表板背景',
    panel_background_image_tips: '当前支持.jpeg,.jpg,.png,.gif文件,大小不要超过15M',
    reUpload: '重新上传',
    create_by: '创建人',
    create_time: '创建时间',
    update_by: '最近修改人',
    update_time: '最近修改时间',
    target_url: '目标URL',
    target_url_tips: '可以点击字段用来拼接URL或者参数',
    select_world: '点击选择字段',
    template_market: '模板市场',
    template_preview: '预览模板',
    apply: '应用',
    apply_this_template: '应用此模板',
    market_network_tips: '查看模板市场模板需要服务器与模板市场(https://dataease.io/templates)连通，请检查网络...',
    enter_name_tips: '请输入仪表板名称',
    name: '名称',
    apply_template: '应用模板',
    enter_template_name_tips: '搜索模板名称',
    pic_adaptation: '适应组件',
    pic_equiratio: '等比适应',
    pic_original: '原始尺寸',
    pic_size: '图片尺寸',
    web_addr: '网页地址',
    stream_media_info: '流媒体信息',
    video_info: '视频信息',
    title_position: '标题位置',
    tab_inner_style: 'tab内部样式',
    data_format: '日期格式',
    border_color: '边框颜色',
    theme_change_warn: '主题更换',
    theme_change_tips: '更换主题将会覆盖视图相关主题属性建议提前备份,是否继续更换？',
    theme_color_change_warn: '主题色更换',
    theme_color_change_tips: '主题色变更将会覆盖原有视图属性',
    theme_color: '主题色',
    theme_color_dark: '深色',
    theme_color_light: '浅色',
    refresh_frequency: '刷新频率',
    card_color_matching: '卡片配色',
    table_color_matching: '表格配色',
    background_color: '背景颜色',
    more: '更多',
    level: '层级',
    enlarge: '放大',
    panel_style: '仪表板样式',
    multiplexing: '复用',
    panel_off: '仪表板已下架',
    batch_opt: '批量操作',
    edit_leave_tips: '是否放弃编辑离开当前界面？',
    hyperlinks: '超链接',
    is_live: '是否直播',
    yes: '是',
    no: '否',
    live_tips: '优先HTTPS链接',
    stream_media_add_tips: '添加流媒体信息...',
    stream_mobile_tips: 'IOS终端可能无法显示',
    json_params_error: '第三方参数解析失败，请检查参数格式是否正确',
    inner_padding: '内边距',
    board_radio: '边框半径',
    background: '背景',
    component_style: '组件样式',
    web_set_tips: '部分网站可能设置不允许嵌入而无法显示',
    repeat_params: '存在名称重复的参数',
    enable_outer_param_set: '启用外部参数设置',
    select_param: '请选择参数...',
    add_param_link_field: '添加参数联动字段',
    add_param: '添加参数',
    enable_param: '启用参数',
    param_name: '参数名称',
    outer_param_set: '外部参数设置',
    outer_param_decode_error: '外部参数解析错误未生效，请按规定编码方式传参',
    input_param_name: '请输入参数名称',
    params_setting: '外部参数设置',
    template_view_tips: '当前是模板视图,请点击更换数据集',
    edit_web_tips: '编辑状态不可操作网页内部',
    no_auth_role: '未分享角色',
    auth_role: '已分享角色',
    picture_limit: '只能插入图片',
    drag_here: '请将左侧字段拖至此处',
    copy_link_passwd: '复制链接及密码',
    copy_link: '复制链接',
    copy_short_link: '复制短链接',
    copy_short_link_passwd: '复制短链接及密码',
    passwd_protect: '密码保护',
    link: '链接',
    over_time: '有效期',
    link_expire: '链接已过期！',
    link_share: '链接分享',
    link_share_desc: '开启链接后，任何人可通过此链接访问仪表板。',
    share: '分享',
    remove_share_confirm: '确认取消当前仪表板所有分享？',
    share_in: '分享给我',
    share_out: '我分享的',
    who_share: '分享人',
    when_share: '分享时间',
    share_to: '分享对象',
    share_to_some: '把[{some}]分享给',
    org: '组织',
    role: '角色',
    user: '用户',
    datalist: '视图列表',
    group: '目录',
    panel: '仪表板',
    panel_list: '仪表板',
    groupAdd: '新建目录',
    panelAdd: '新建仪表板',
    delete: '删除',
    move_to: '移动到',
    rename: '重命名',
    import: '导入模板',
    tips: '提示',
    confirm_delete: '确认删除',
    delete_success: '删除成功',
    confirm: '确认',
    cancel: '取消',
    search: '搜索',
    back: '返回',
    view: '视图',
    module: '组件',
    filter_module: '过滤组件',
    select_by_module: '按组件选择',
    edit: '编辑',
    sys_template: '系统模板',
    user_template: '用户模板',
    add_category: '添加分类',
    add_app_category: '添加应用分类',
    filter_keywords: '输入关键字进行过滤',
    dashboard_theme: '仪表板主题',
    table: '表格',
    gap: '有间隙',
    no_gap: '无间隙',
    component_gap: '组件间隙',
    refresh_time: '刷新时间',
    minute: '分钟',
    second: '秒',
    photo: '图片',
    default_panel: '默认仪表板',
    create_public_links: '创建公共链接',
    to_default: '另存为默认',
    to_default_panel: '另存为默认仪表板',
    store: '收藏',
    save_to_panel: '保存为模板',
    export_to_panel: '导出为模板',
    export_to_pdf: '导出为PDF',
    export_to_img: '导出为图片',
    export_to_app: '导出为应用',
    preview: '预览',
    fullscreen_preview: '全屏预览',
    new_tab_preview: '新Tab页预览',
    select_panel_from_left: '请从左侧选择仪表板',
    template_nale: '模板名称',
    template: '模板',
    category: '分类',
    all_org: '所有组织',
    custom: '自定义',
    import_template: '导入模板',
    copy_template: '复用模板',
    upload_template: '上传模板',
    belong_to_category: '所属类别',
    pls_select_belong_to_category: '请选择所属类别',
    template_name_cannot_be_empty: '模板名称不能为空',
    select_by_table: '按表选择',
    data_list: '数据列表',
    component_list: '组件列表',
    custom_scope: '控制范围',
    binding_parameters: '参数',
    multiple_choice: '多选',
    show_time: '显示时间',
    single_choice: '单选',
    field: '字段',
    unshared_people: '未分享人员',
    shared_people: '已分享人员',
    error_data: '获取数据出错，请联系管理员',
    canvas_size: '画布大小',
    canvas_scale: '画布比例',
    style: '样式',
    clean_canvas: '清空画布',
    insert_picture: '插入图片',
    redo: '重做',
    undo: '撤销',
    panelNull: '这是个空的仪表板，可以通过编辑来丰富内容',
    copy: '复制',
    paste: '粘贴',
    cut: '剪切',
    lock: '锁定',
    topComponent: '置顶',
    bottomComponent: '置底',
    upComponent: '上移',
    downComponent: '下移',
    linkage_setting: '联动设置',
    add_tab: '新增Tab',
    open_aided_design: '打开组件辅助设计',
    close_aided_design: '关闭组件辅助设计',
    open_style_design: '打开样式设计',
    close_style_design: '关闭样式设计',
    matrix_design: '矩阵设计',
    left: 'x 坐标',
    top: 'y 坐标',
    height: '高',
    width: '宽',
    color: '颜色',
    backgroundColor: '背景色',
    borderStyle: '边框风格',
    borderWidth: '边框宽度',
    borderColor: '边框颜色',
    borderRadius: '边框半径',
    fontSize: '字体大小',
    fontWeight: '字体粗细',
    lineHeight: '行高',
    letterSpacing: '字间距',
    padding: '内间距',
    margin: '外间距',
    textAlign: '左右对齐',
    opacity: '不透明度',
    verticalAlign: '上下对齐',
    text_align_left: '左对齐',
    text_align_center: '左右居中',
    text_align_right: '右对齐',
    vertical_align_top: '上对齐',
    vertical_align_middle: '居中对齐',
    vertical_align_bottom: '下对齐',
    border_style_solid: '实线',
    border_style_dashed: '虚线',
    select_component: '请选择组件',
    other_module: '其他',
    content: '内容',
    default_panel_name: '默认仪表板名称',
    source_panel_name: '原仪表板名称',
    content_style: '内容样式',
    canvas_self_adaption: '自适应画布区域',
    panel_save_tips: '仪表板已变动，是否保存？',
    panel_save_warn_tips: '如果未保存，你对仪表板做的变更将会丢失！',
    do_not_save: '不保存',
    save: '保存',
    drill: '下钻',
    linkage: '联动',
    jump: '跳转',
    cancel_linkage: '取消联动',
    switch_picture: '更换图片',
    select_field: '选择视图字段',
    remove_all_linkage: '清除所有联动',
    exit_un_march_linkage_field: '存在未匹配联动关系的字段',
    details: '详情',
    setting: '设置',
    no_drill_field: '缺少关联字段',
    matrix: '矩阵',
    suspension: '悬浮',
    new_element_distribution: '元素移入分布方式',
    aided_grid: '辅助设计网格',
    aided_grid_open: '打开',
    aided_grid_close: '关闭',
    subject_no_edit: '系统主题不能修改',
    subject_name_not_null: '主题名称需要1~20字符',
    is_enable: '是否启用',
    open_mode: '打开方式',
    new_window: '新开页面',
    now_window: '当前页面',
    hyperLinks: '目标地址',
    link_open_tips: '仪表板非编辑状态可打开链接',
    data_loading: '数据准备中...',
    export_loading: '导出中...',
    export_pdf: '导出PDF',
    jump_set: '跳转设置',
    enable_jump: '启用跳转',
    column_name: '字段名称',
    enable_column: '启用字段',
    open_model: '打开方式',
    link_type: '链接类型',
    link_outer: '外部链接',
    link_panel: '仪表板',
    select_jump_panel: '选择关联的仪表板',
    link_view: '联动视图',
    link_view_field: '联动视图字段',
    add_jump_field: '追加跳转联动依赖字段',
    input_jump_link: '请输入跳转连接',
    select_dimension: '请选择维度...',
    please_select: '请选择',
    video_type: '视频类型',
    online_video: '在线视频',
    streaming_media: '流媒体',
    auto_play: '自动播放',
    video_tips: '优先HTTPS链接；当前支持格式mp4,webm',
    play_frequency: '播放频率',
    play_once: '播放一次',
    play_circle: '循环播放',
    video_links: '视频链接',
    web_url: '网页地址',
    video_add_tips: '添加视频信息...',
    link_add_tips_pre: '请点击上方',
    web_add_tips_suf: '添加网页信息...',
    panel_view_result_show: '视图结果',
    panel_view_result_tips: '选择仪表板会覆盖视图的结果展示数量，取值范围1~10000',
    timeout_refresh: '请求超时，稍后刷新...',
    mobile_layout: '移动端布局',
    component_hidden: '隐藏的组件',
    public_link_tips: '当前是公共链接模式，目标仪表板未设置公共链接，无法跳转',
    input_title: '请输入标题',
    show_title: '标题',
    default_settings: '默认值设置',
    choose_background: '选择组件背景',
    choose_background_tips: '组件自有背景会覆盖当前设置',
    setting_background: '设置背景',
    setting_jump: '跳转设置',
    select_view: '请选择视图...',
    visual: '虚拟化',
    prohibit_multiple: '禁止同数据集多字段',
    be_empty_dir: '是空目录！',
    fold: '收起',
    expand: '展开',
    pdf_export: 'PDF 导出',
    switch_pdf_template: '切换 PDF 模板'
  },
  plugin: {
    local_install: '本地安装',
    remote_install: '远程安装',
    name: '插件名称',
    free: '是否免费',
    cost: '费用',
    descript: '描述',
    version: '版本',
    creator: '作者',
    install_time: '安装时间',
    release_time: '时间',
    un_install: '卸载(卸载并重启服务后生效)',
    uninstall_confirm: '确定卸载该插件',
    uninstall_cancel: '取消卸载插件',
    un_install_success: '卸载成功，重启生效',
    un_install_error: '卸载失败，请联系管理员'
  },
  display: {
    favicon: '系统图标',
    logo: '头部系统logo',
    loginLogo: '登录页面头部logo',
    loginImage: '登录页面右侧图片',
    loginTitle: '登录页面标题',
    title: '系统名称',
    advice_size: '建议图片大小',
    theme: '主题颜色',
    topMenuColor: '头部背景',
    topMenuActiveColor: '头部选中背景',
    topMenuTextColor: '头部字体颜色',
    topMenuTextActiveColor: '头部字体选中颜色',
    themeLight: '浅色',
    themeDark: '深色',
    themeCustom: '自定义',
    openHomePage: '显示首页',
    openMarketPage: '显示模板市场',
    mobileBG: '移动端登录页背景',
    helpLink: '帮助文档链接',
    homeLink: '首页链接',
    defaultHomeLink: '默认为系统内置首页',
    showFoot: '显示登录页脚',
    footContent: '页脚内容',
    webFormat: '请输入以[https://或http://]开头的正确网址'
  },
  auth: {
    no_item_selected: '请在左侧选择组织、角色或用户',
    no_resource_selected: '请在左侧选择资源',
    search_pre: '通过',
    search_suf: '名称来搜索',
    separate_auth: '单独授权',
    auth_extent_tips: '继承自以下组织或角色的权限:',
    authConfig: '按用户配置权限',
    sourceConfig: '按资源配置权限',
    authQuickConfig: '权限快捷配置',
    dept: '组织',
    role: '角色',
    user: '用户',
    set_rules: '设置规则',
    sysParams: '系统变量',
    sysParams_type: {
      user_id: '用户ID',
      user_name: '用户名',
      user_source: '用户来源',
      user_email: '邮箱',
      dept: '组织',
      role: '角色'
    },
    linkAuth: '数据源',
    datasetAuth: '数据集',
    chartAuth: '视图',
    panelAuth: '仪表板',
    menuAuth: '菜单和操作',
    deptHead: '所有组织',
    roleHead: '所有角色',
    userHead: '所有用户',
    linkAuthHead: '所有数据源',
    datasetAuthHead: '所有数据集',
    chartAuthHead: '所有视图',
    panelAuthHead: '所有仪表板',
    menuAuthHead: '所有菜单和操作',
    view: '查看',
    use: '使用',
    export: '导出',
    manage: '管理',
    row_column: '行列权限设置',
    row_permission: '行权限规则',
    enable_row: '启用行权限',
    white_list: '白名单',
    white_user_not: '以上权限规则对白名单用户不生效',
    organization_or_role: '请选择组织或角色',
    column_permission: '列权限规则',
    enable_column: '启用列权限',
    search_by_field: '通过字段名称搜索',
    add_condition: '添加条件',
    add_relationship: '添加关系',
    filter_fields: '筛选字段',
    selct_filter_fields: '请选择筛选字段',
    enter_keywords: '请输关键字',
    screen_method: '筛选方式',
    select: '请选择',
    fixed_value: '固定值',
    default_method: '默认条件',
    select_all: '全 选',
    added: '已添加',
    manual_input: '手工输入',
    please_fill: '请一行填一个，最多添加500个,识别录入时会自动过滤重复的选项和已经添加过的选项',
    close: '关 闭',
    add: '添 加',
    sure: '确 定'
  },
  about: {
    auth_to: '授权给',
    invalid_license: 'License 无效',
    update_license: '更新 License',
    expiration_time: '过期时间',
    expirationed: '(已过期)',
    auth_num: '授权数量',
    version: '版本',
    version_num: '版本号',
    standard: '标准版',
    enterprise: '企业版',
    support: '获取技术支持',
    update_success: '更新成功',
    serial_no: '序列号',
    remark: '备注'
  },
  template: {
    exit_same_template_check: '当前分类存在相同名称模板，是否覆盖？',
    override: '覆盖',
    cancel: '取消',
    confirm_upload: '上传确认'
  },
  cron: {
    second: '秒',
    minute: '分',
    hour: '时',
    day: '日',
    minute_default: '分 (执行时间：0秒)',
    hour_default: '时 (执行时间：0分0秒)',
    day_default: '日 (执行时间：0时0分0秒)',
    month: '月',
    week: '周',
    year: '年',
    d_w_cant_not_set: '日期与星期不可以同时为“不指定”',
    d_w_must_one_set: '日期与星期必须有一个为“不指定”',
    every_day: '每日',
    cycle: '周期',
    not_set: '不指定',
    from: '从',
    to: '至',
    repeat: '循环',
    day_begin: '日开始，每',
    day_exec: '日执行一次',
    work_day: '工作日',
    this_month: '本月',
    day_near_work_day: '号，最近的工作日',
    this_week_last_day: '本月最后一天',
    set: '指定',
    every_hour: '每时',
    hour_begin: '时开始，每',
    hour_exec: '时执行一次',
    every_month: '每月',
    month_begin: '月开始，每',
    month_exec: '月执行一次',
    every: '每',
    every_begin: '开始，每',
    every_exec: '执行一次',
    every_week: '每周',
    week_start: '从星期',
    week_end: '至星期',
    every_year: '每年',
    week_tips: '说明：1-7 分别对应 周日-周六',
    minute_limit: '分钟不能小于1，大于59',
    hour_limit: '小时不能小于1，大于23',
    day_limit: '天不能小于1，大于31'
  },
  dept: {
    can_not_move_change_sort: '不能移动以改变排序',
    can_not_move_parent_to_children: '父组织不能移动到自己的子节点下',
    move_success: '移动成功',
    name_exist_pre: '下已存在名称为【',
    name_exist_suf: '】的组织',
    root_org: '顶层组织'
  },
  webmsg: {
    web_msg: '站内消息通知',
    show_more: '查看更多',
    all_type: '全部类型',
    panel_type: '仪表板分享',
    dataset_type: '数据集同步',
    content: '消息内容',
    sned_time: '提交时间',
    read_time: '查看时间',
    type: '消息类型',
    mark_read: '标记已读',
    all_mark_read: '全部已读',
    please_select: '请至少选择一条消息',
    mark_success: '标记已读成功',
    receive_manage: '接收管理',
    i18n_msg_type_panel_share: '仪表板分享',
    i18n_msg_type_panel_share_cacnel: '仪表板取消分享',
    i18n_msg_type_dataset_sync: '数据集同步',
    i18n_msg_type_dataset_sync_success: '数据集同步成功',
    i18n_msg_type_dataset_sync_failed: '数据集同步失败',
    i18n_msg_type_ds_invalid: '数据源失效',
    i18n_msg_type_all: '全部类型',
    channel_inner_msg: '站内消息',
    channel_email_msg: '邮件提醒',
    channel_wecom_msg: '企业微信',
    channel_dingtalk_msg: '钉钉提醒',
    channel_lark_msg: '飞书提醒',
    channel_larksuite_msg: '国际飞书'
  },
  denumberrange: {
    label: '数值区间',
    split_placeholder: '至',
    please_key_min: '请输入最小值',
    please_key_max: '请输入最大值',
    out_of_min: '最小值不能小于最小整数-2³²',
    out_of_max: '最大值不能大于最大整数2³²-1',
    must_int: '请输入整数',
    min_out_max: '最小值必须小于最大值',
    max_out_min: '最大值必须大于最小值'
  },
  desearchbutton: {
    label: '查询按钮',
    text: '名称',
    auto_trigger: '自动触发',
    range: '控制范围',
    relative: '关联组件',
    auto_trigger_tip: '进入预览页面自动触发一次',
    range_tip: '默认关联全部过滤组件'
  },
  desresetbutton: {
    label: '清空按钮',
    reset: '重置按钮',
    text: '名称'
  },
  denumberselect: {
    label: '数字下拉',
    placeholder: '请选择数字'
  },
  deinputsearch: {
    label: '文本搜索',
    placeholder: '请输入关键字'
  },
  detextselect: {
    label: '文本下拉',
    placeholder: '请选择文本'
  },
  detextselectTree: {
    label: '下拉树',
    placeholder: '请选择'
  },
  detextgridselect: {
    label: '文本列表',
    placeholder: '请选择'
  },
  denumbergridselect: {
    label: '数字列表',
    placeholder: '请选择'
  },
  dedaterange: {
    label: '日期范围',
    to_placeholder: '结束日期',
    from_placeholder: '开始日期',
    split_placeholder: '至'
  },
  dedate: {
    label: '日期',
    placeholder: '请选择日期'
  },
  deyearmonth: {
    label: '年月',
    placeholder: '请选择年月'
  },
  deyear: {
    label: '年份',
    placeholder: '请选择年份'
  },
  deshowdate: {
    label: '时间',
    show_week: '显示星期',
    show_date: '显示日期',
    time_format: '时间格式',
    date_format: '日期格式',
    custom: '自定义格式',
    open_mode: '展示风格',
    m_default: '简单风格',
    m_elec: '电子时钟',
    m_simple: '简单表盘',
    m_complex: '复杂表盘',
    select_openMode: '请选择展示风格',
    select_time_format: '请选择时间格式',
    select_date_format: '请选择日期格式'

  },
  xpacktask: {
    add: '新增任务',
    edit: '编辑任务',
    task_id: '任务ID',
    name: '任务名称',
    last_exec_time: '上次执行时间',
    last_exec_status: '上次执行状态',
    ready: '就绪',
    success: '成功',
    underway: '执行中',
    error: '失败',
    creator: '创建人',
    create_time: '创建时间',
    search_by_name: '根据名称搜索',
    exec_time: '执行时间',
    status: '执行状态',
    task_status: '任务状态',
    running: '运行中',
    stopped: '已停止',
    start: '启用',
    start_success: '启用成功',
    start_success_but: '，但任务过期，请手动更改结束时间',
    sure_batch_delete: '确定批量删除任务吗？',
    pixel_error: '分辨率支持{800 - 10000} * {500 - 6250}',
    next_exec_time: '下次执行时间'

  },
  emailtask: {
    week_mon: '周一',
    week_tue: '周二',
    week_wed: '周三',
    week_thu: '周四',
    week_fri: '周五',
    week_sat: '周六',
    week_sun: '周日',
    send_config: '发送设置',
    title: '报告主题',
    panel: '仪表板',
    content: '报告正文',
    recipients: '收件人',
    recisetting: '接收设置',
    email: '邮件',
    wecom: '企业微信',
    dingtalk: '钉钉',
    lark: '飞书',
    larksuite: '国际飞书',
    pixel: '分辨率',
    default: '默认',
    custom: '自定义',
    rate_type: '发送频率',
    cron_exp: 'cron表达式',
    exec_time: '执行时间',
    start_time: '开始时间',
    end_time: '结束时间',
    chart_data: '视图数据',
    panel_preview: '预览报告',
    preview: '预览',
    emial_preview: '报告预览',
    chart_data_range: '视图数据范围',
    simple_repeat: '简单重复',
    once_a_day: '每天',
    once_a_week: '每周',
    once_a_month: '每月',
    complex_repeat: '复杂重复',
    pixel_tip: '可直接输入自定义分辨率(例如:2560 * 1600)或选择',
    task_type: '任务类型',
    range_view: '展示数据',
    range_all: '全部数据',
    execute_now: '立即执行',
    fire_now_success: '任务发起成功'
  },
  dynamic_time: {
    set_default: '设置默认值',
    fix: '固定时间',
    dynamic: '动态时间',
    relative: '相对当前',
    today: '今天',
    yesterday: '昨天',
    firstOfMonth: '月初',
    firstOfYear: '年初',
    custom: '自定义',
    date: '日',
    week: '周',
    month: '月',
    year: '年',
    before: '前',
    after: '后',
    preview: '预览',
    set: '设置',

    cweek: '本周',
    lweek: '上周',
    cmonth: '本月',
    cquarter: '本季',
    lquarter: '上季',
    cyear: '本年'
  },
  dynamic_year: {
    fix: '固定年份',
    dynamic: '动态年份',
    current: '今年',
    last: '去年'
  },
  dynamic_month: {
    fix: '固定年月',
    dynamic: '动态年月',
    current: '当月',
    last: '上月',
    firstOfYear: '当年首月',
    sameMonthLastYear: '去年同月'
  },
  wizard: {
    welcome_title: '欢迎使用DataEase',
    welcome_hint: '人人可用的开源数据可视化分析工具',
    demo_video: '演示视频',
    quick_start: '快速入门',
    online_document: '在线文档',
    latest_developments: '最新动态',
    teaching_video: '教学视频',
    enterprise_edition: '企业版',
    contact_us: '联系我们',
    demo_video_hint: '如何3分钟制作一个DataEase数据看板、并分享给他人',
    online_document_hint: '涵盖DataEase的安装步骤、用户手册、使用教程、常见问题的解决方案、以及二次开发等',
    teaching_video_bottom_hint: '更多视频资料',
    enterprise_edition_hint1: '提供企业级应用场景X-Pack增强包',
    enterprise_edition_hint2: '提供高等级原厂服务支持',
    enterprise_edition_hint3: '提供DataEase最佳实践建议',
    open_source_community: '开源社区',
    click_show: '点击查看',
    show_more: '查看更多',
    click_inner: '点击进入',
    email: '邮箱：',
    tel: '电话：',
    web: '网址：',
    apply: '免费试用申请',
    more: '更多',
    weChat_official_account: '微信公众号',
    technical_group: '技术交流群',
    f2c_train: '飞致云认证'
  },
  kettle: {
    add: '添加 Kettle 服务',
    status: '状态',
    carte: 'Kettle 地址',
    port: '端口',
    user: '用户名',
    passwd: '密码'
  },
  log: {
    title: '操作日志',
    optype: '操作类型',
    detail: '操作详情',
    user: '操作用户',
    time: '操作时间',
    export: '导出',
    export_as: '导出为',
    confirm: '确定导出吗？',
    search_by_key: '搜索详情',
    ip: 'IP地址'
  },
  plugin_style: {
    border: '边框'
  },
  sql_variable: {
    variable_mgm: '参数设置'
  },
  map_setting: {
    area_level: '区域等级',
    area_code: '区域代码',
    please_input: '请填写',
    parent_area: '上级区域',
    area_code_tip: '区域代码格式为9位数字',
    area_name: '区域名称',
    parent_name: '上级区域',
    geo_json: '坐标文件',
    fileplaceholder: '请上传json格式坐标文件',
    delete_confirm: '及子节点都会被删除，确认执行？',
    cur_node: '当前节点'
  },
  map_mapping: {
    map: '图形',
    attr: '属性',
    empty: '无数据',
    please_select_map: '请先选择地图范围'
  },
  'I18N_USER_TEMPLATE_ERROR': '模版错误',
  'i18n_max_user_import_size': '文件最大不能超过10M',
  app_template: {
    move: '移动',
    move_item: '移动应用',
    datasource_new: '新建',
    datasource_history: '复用',
    datasource_from: '数据来源',
    apply_template: '应用模板',
    execution_time: '执行时间',
    app_manager: '应用管理',
    app_upload: '上传应用',
    no_apps: '暂无应用',
    app_group_icon: '封面图标',
    app_name: '应用名称',
    search_by_keyword: '通过关键字搜索',
    apply_logs: '应用记录',
    app_group_delete_tips: '确定删除该应用分类吗?',
    app_group_delete_content: '删除后，该分类中所有的应用模板也将被删除。',
    panel_position: '仪表板位置',
    panel_name: '仪表板名称',
    dataset_group_position: '数据集分组位置',
    dataset_group_name: '数据集分组名称',
    datasource_info: '数据源信息',
    datasource: '数据源',
    dataset_group: '数据集分组',
    panel: '仪表板',
    log_delete_tips: '确定删除该条应用记录吗？',
    log_resource_delete_tips: '删除相关资源（删除后不可恢复）',
    file_error_tips: '未找到相关数据文件，当前文件可能不是DataEase应用文件，或者文件已经损坏',
    app_export: '应用导出',
    app_version: '应用版本',
    program_version: 'DataEase最低版本',
    creator: '作者',
    export: '导出'
  },

  logout: {
    oidc_logout_error: 'OIDC退出失败，是否继续退出DataEase？',
    cas_logout_error: 'CAS服务异常，请联系管理员！'
  },
  watermark: {
    support_params: '当前支持的参数：',
    enable: '启用',
    enable_panel_custom: '允许仪表板单独打开或者关闭水印',
    content: '内容',
    custom_content: '自定义公式',
    account: '账号',
    nick_name: '昵称',
    ip: 'IP',
    now: '当前时间',
    watermark_color: '水印颜色',
    watermark_font_size: '水印字号',
    watermark_space: '水印间距',
    horizontal: '横向间距',
    vertical: '纵向间距',
    reset: '重置',
    preview: '预览',
    save: '保存'
  }
}
